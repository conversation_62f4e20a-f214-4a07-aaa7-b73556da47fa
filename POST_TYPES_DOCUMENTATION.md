# Custom Post Types Documentation - Galib Theme

This document explains how to use the newly created "Team" and "Blog" post types in your Publii site using the custom "Galib" theme.

## Overview

Two custom post types have been created:
1. **Team** - For team member profiles and information
2. **Blog** - For enhanced blog posts with additional features

## How to Use

### Creating Team Posts

1. **Create a new post** in Publii
2. **Select the "Team Member Profile" template** from the template dropdown
3. **Add the "Team" tag** to categorize the post
4. **Fill in the custom fields** (see below for details)
5. **Add a featured image** (recommended: square image for best results with circular display)

#### Team Post Custom Fields

The team post type supports the following custom fields that you can add via the post's additional data:

- **position**: The team member's job title or role
- **bio**: A detailed biography of the team member
- **social_links**: Social media links in JSON format
- **skills**: Skills and expertise areas

#### Example Custom Fields Usage

In the Publii editor, you can add these fields in the "Additional Data" section:

```json
{
  "position": "Senior Developer",
  "bio": "<PERSON> has over 10 years of experience in web development...",
  "social_links": "{\"linkedin\": \"https://linkedin.com/in/johndoe\", \"twitter\": \"https://twitter.com/johndoe\"}",
  "skills": "JavaScript, React, Node.js, Python"
}
```

### Creating Blog Posts

1. **Create a new post** in Publii
2. **Select the "Blog Post" template** from the template dropdown
3. **Add the "Blog" tag** to categorize the post
4. **Fill in the custom fields** (see below for details)

#### Blog Post Custom Fields

The blog post type supports the following custom fields:

- **category**: Blog category (Technology, Lifestyle, Business, Travel, Other)
- **reading_time**: Estimated reading time (e.g., "5 min read")
- **featured**: Checkbox to mark as a featured post

#### Example Custom Fields Usage

```json
{
  "category": "Technology",
  "reading_time": "8 min read",
  "featured": true
}
```

## Template Features

### Team Template Features

- **Circular profile photo** display
- **Position/role** prominently displayed
- **Biography section** with styled background
- **Skills section** with highlighted display
- **Social media links** with styled buttons
- **Responsive design** for mobile devices

### Blog Template Features

- **Category badge** at the top of the post
- **Featured post badge** with animation for featured posts
- **Reading time indicator** in the meta information
- **Enhanced typography** for better readability
- **Code syntax highlighting** support
- **Styled blockquotes** and other content elements

## Styling

Custom CSS has been added to enhance the appearance of both post types:

- **Team posts** have a professional, profile-focused design
- **Blog posts** have enhanced readability and modern styling
- **Responsive design** ensures good display on all devices
- **Dark mode support** for themes that support it

## Tags for Organization

Two tags have been created to help organize your content:

- **Team** tag: Use for all team member posts
- **Blog** tag: Use for all blog posts

These tags will help visitors filter and find specific types of content on your site.

## File Structure

The following files have been added/modified:

### New Template Files
- `input/themes/galib/post-team.hbs` - Team member template
- `input/themes/galib/post-blog.hbs` - Blog post template

### New CSS Files
- `input/themes/galib/assets/css/post-types.css` - Custom styles for post types

### Modified Files
- `input/themes/galib/config.json` - Added post template definitions
- `input/themes/galib/partials/head.hbs` - Added CSS include
- `input/config/theme.config.json` - Added post type configurations

### Database Changes
- Added "Team" and "Blog" tags to the tags table

## Best Practices

### For Team Posts
1. Use square images (1:1 aspect ratio) for profile photos
2. Keep position titles concise and descriptive
3. Write engaging biographies that highlight achievements
4. Include relevant social media links
5. List key skills and expertise areas

### For Blog Posts
1. Choose appropriate categories for better organization
2. Estimate reading time accurately (roughly 200 words per minute)
3. Use the featured flag sparingly for your best content
4. Write compelling titles and meta descriptions
5. Use proper heading structure (H2, H3, etc.) for better SEO

## Troubleshooting

### Templates Not Showing
- Ensure the template files are in the correct theme directory
- Check that the theme config.json includes the postTemplates section
- Restart Publii if templates don't appear in the dropdown

### Custom Fields Not Working
- Verify custom field names match exactly (case-sensitive)
- Ensure JSON format is valid for complex fields like social_links
- Check that the theme configuration includes the field definitions

### Styling Issues
- Confirm post-types.css is included in the head.hbs partial
- Check browser developer tools for CSS loading errors
- Verify CSS selectors match the template structure

## Future Enhancements

Potential improvements that could be added:

1. **Custom post type admin interface** for easier field management
2. **Additional field types** (date picker, image upload, etc.)
3. **Post type-specific archives** and filtering
4. **Schema markup** for better SEO
5. **Social media integration** for automatic sharing

## Support

If you encounter any issues with these post types, check:

1. Publii version compatibility
2. Theme file permissions
3. Database integrity
4. CSS and JavaScript console errors

For additional customization, you can modify the template files and CSS to match your specific needs and branding requirements.
