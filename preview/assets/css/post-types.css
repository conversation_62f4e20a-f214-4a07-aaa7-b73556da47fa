/* Team Post Type Styles */
.post--team .team-position {
    margin: 1rem 0 2rem;
    text-align: center;
}

.post--team .team-position__title {
    color: var(--primary-color, #057B12);
    font-size: 1.5rem;
    font-weight: 500;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.post--team .team-photo {
    border-radius: 50%;
    overflow: hidden;
    max-width: 300px;
    margin: 2rem auto;
}

.post--team .team-photo .hero__image-wrapper {
    border-radius: 50%;
    overflow: hidden;
}

.post--team .team-bio {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.post--team .team-bio h3 {
    color: var(--primary-color, #057B12);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.post--team .team-skills {
    background: #e8f5e8;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
    border-left: 4px solid var(--primary-color, #057B12);
}

.post--team .team-skills h3 {
    color: var(--primary-color, #057B12);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.post--team .team-skills__content {
    font-weight: 500;
}

.post--team .team-social {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    margin: 2rem 0;
}

.post--team .team-social h3 {
    color: var(--primary-color, #057B12);
    margin-bottom: 1.5rem;
}

.post--team .team-social__links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.post--team .team-social__links a {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--primary-color, #057B12);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.post--team .team-social__links a:hover {
    background: var(--primary-dark-color, #00FF21);
    transform: translateY(-2px);
}

/* Blog Post Type Styles */
.post--blog .blog-category {
    margin-bottom: 1rem;
}

.post--blog .blog-category__label {
    display: inline-block;
    background: var(--primary-color, #057B12);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.post--blog .blog-featured {
    margin: 1rem 0;
}

.post--blog .blog-featured__badge {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b6b, #ffa500);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(255, 107, 107, 0.5);
    }
    100% {
        box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
    }
}

.post--blog .blog-reading-time {
    display: inline-block;
    background: #6c757d;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.post--blog .blog-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.post--blog .blog-content h2,
.post--blog .blog-content h3,
.post--blog .blog-content h4 {
    color: var(--primary-color, #057B12);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.post--blog .blog-content blockquote {
    border-left: 4px solid var(--primary-color, #057B12);
    background: #f8f9fa;
    padding: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 8px 8px 0;
}

.post--blog .blog-content code {
    background: #f1f3f4;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
}

.post--blog .blog-content pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 2rem 0;
}

.post--blog .blog-content pre code {
    background: none;
    padding: 0;
    color: inherit;
}

/* Responsive Design */
@media (max-width: 768px) {
    .post--team .team-photo {
        max-width: 200px;
    }
    
    .post--team .team-bio,
    .post--team .team-skills,
    .post--team .team-social {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .post--team .team-social__links {
        flex-direction: column;
        align-items: center;
    }
    
    .post--blog .blog-content {
        font-size: 1rem;
    }
    
    .post--blog .blog-content pre {
        padding: 1rem;
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .post--team .team-social,
    .post--blog .blog-featured__badge {
        display: none;
    }
    
    .post--team .team-bio,
    .post--team .team-skills {
        background: none;
        border: 1px solid #ddd;
    }
}

/* Dark Mode Support (if theme supports it) */
@media (prefers-color-scheme: dark) {
    .post--team .team-bio {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .post--team .team-skills {
        background: #1a202c;
        color: #e2e8f0;
    }
    
    .post--team .team-social {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        color: #e2e8f0;
    }
    
    .post--blog .blog-content blockquote {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .post--blog .blog-content code {
        background: #4a5568;
        color: #e2e8f0;
    }
}
