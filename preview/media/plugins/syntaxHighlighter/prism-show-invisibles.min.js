!function(){if("undefined"!=typeof Prism){var r={tab:/\t/,crlf:/\r\n/,lf:/\n/,cr:/\r/,space:/ /};Prism.hooks.add("before-highlight",function(r){e(r.grammar)})}function a(r,n){var t=r[n],i=Prism.util.type(t);switch(i){case"RegExp":var f={};r[n]={pattern:t,inside:f},e(f);break;case"Array":for(var o=0,s=t.length;o<s;o++)a(t,o);break;default:var f=t.inside||(t.inside={});e(f)}}function e(n){if(n&&!n.tab){for(var t in r)r.hasOwnProperty(t)&&(n[t]=r[t]);for(var t in n)n.hasOwnProperty(t)&&!r[t]&&("rest"===t?e(n.rest):a(n,t))}}}();