(function(){if(typeof self==="undefined"||!self.Prism||!self.document){return}var d="line-numbers";var a=/\n(?!$)/g;var b=function(h){var g=c(h);var k=g["white-space"];if(k==="pre-wrap"||k==="pre-line"){var j=h.querySelector("code");var e=h.querySelector(".line-numbers-rows");var i=h.querySelector(".line-numbers-sizer");var f=j.textContent.split(a);if(!i){i=document.createElement("span");i.className="line-numbers-sizer";j.appendChild(i)}i.style.display="block";f.forEach(function(m,l){i.textContent=m||"\n";var n=i.getBoundingClientRect().height;e.children[l].style.height=n+"px"});i.textContent="";i.style.display="none"}};var c=function(e){if(!e){return null}return window.getComputedStyle?getComputedStyle(e):(e.currentStyle||null)};window.addEventListener("resize",function(){Array.prototype.forEach.call(document.querySelectorAll("pre."+d),b)});Prism.hooks.add("complete",function(i){if(!i.code){return}var j=i.element.parentNode;var k=/\s*\bline-numbers\b\s*/;if(!j||!/pre/i.test(j.nodeName)||(!k.test(j.className)&&!k.test(i.element.className))){return}if(i.element.querySelector(".line-numbers-rows")){return}if(k.test(i.element.className)){i.element.className=i.element.className.replace(k," ")}if(!k.test(j.className)){j.className+=" line-numbers"}var h=i.code.match(a);var g=h?h.length+1:1;var e;var f=new Array(g+1);f=f.join("<span></span>");e=document.createElement("span");e.setAttribute("aria-hidden","true");e.className="line-numbers-rows";e.innerHTML=f;if(j.hasAttribute("data-start")){j.style.counterReset="linenumber "+(parseInt(j.getAttribute("data-start"),10)-1)}i.element.appendChild(e);b(j);Prism.hooks.run("line-numbers",i)});Prism.hooks.add("line-numbers",function(e){e.plugins=e.plugins||{};e.plugins.lineNumbers=true});Prism.plugins.lineNumbers={getLine:function(f,g){if(f.tagName!=="PRE"||!f.classList.contains(d)){return}var j=f.querySelector(".line-numbers-rows");var h=parseInt(f.getAttribute("data-start"),10)||1;var e=h+(j.children.length-1);if(g<h){g=h}if(g>e){g=e}var i=g-h;return j.children[i]}}}());