!function(){if("undefined"!=typeof Prism){var n=/\b([a-z]{3,7}:\/\/|tel:)[\w\-+%~/.:=&!$'()*,;@]+(?:\?[\w\-+%~/.:=?&!$'()*,;@]*)?(?:#[\w\-+%~/.:#=?&!$'()*,;@]*)?/,i=/\b\S+@[\w.]+[a-z]{2}/,t=/\[([^\]]+)\]\(([^)]+)\)/,e=["comment","url","attr-value","string"];Prism.plugins.autolinker={processGrammar:function(a){a&&!a["url-link"]&&(Prism.languages.DFS(a,function(a,r,l){e.indexOf(l)>-1&&!Array.isArray(r)&&(r.pattern||(r=this[a]={pattern:r}),r.inside=r.inside||{},"comment"==l&&(r.inside["md-link"]=t),"attr-value"==l?Prism.languages.insertBefore("inside","punctuation",{"url-link":n},r):r.inside["url-link"]=n,r.inside["email-link"]=i)}),a["url-link"]=n,a["email-link"]=i)}},Prism.hooks.add("before-highlight",function(n){Prism.plugins.autolinker.processGrammar(n.grammar)}),Prism.hooks.add("wrap",function(n){if(/-link$/.test(n.type)){n.tag="a";var i=n.content;if("email-link"==n.type&&0!=i.indexOf("mailto:"))i="mailto:"+i;else if("md-link"==n.type){var e=n.content.match(t);i=e[2],n.content=e[1]}n.attributes.href=i;try{n.content=decodeURIComponent(n.content)}catch(a){}}})}}();