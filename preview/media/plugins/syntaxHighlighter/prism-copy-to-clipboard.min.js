(function(){if(typeof self==="undefined"||!self.Prism||!self.document){return}var c=[];var e={};var b=function(){};Prism.plugins.toolbar={};var a=Prism.plugins.toolbar.registerButton=function(f,g){var h;if(typeof g==="function"){h=g}else{h=function(j){var i;if(typeof g.onClick==="function"){i=document.createElement("button");i.type="button";i.addEventListener("click",function(){g.onClick.call(this,j)})}else{if(typeof g.url==="string"){i=document.createElement("a");i.href=g.url}else{i=document.createElement("span")}}i.textContent=g.text;return i}}c.push(e[f]=h)};var d=Prism.plugins.toolbar.hook=function(f){var h=f.element.parentNode;if(!h||!/pre/i.test(h.nodeName)){return}if(h.parentNode.classList.contains("code-toolbar")){return}var i=document.createElement("div");i.classList.add("code-toolbar");h.parentNode.insertBefore(i,h);i.appendChild(h);var g=document.createElement("div");g.classList.add("toolbar");if(document.body.hasAttribute("data-toolbar-order")){c=document.body.getAttribute("data-toolbar-order").split(",").map(function(j){return e[j]||b})}c.forEach(function(l){var j=l(f);if(!j){return}var k=document.createElement("div");k.classList.add("toolbar-item");k.appendChild(j);g.appendChild(k)});i.appendChild(g)};a("label",function(h){var j=h.element.parentNode;if(!j||!/pre/i.test(j.nodeName)){return}if(!j.hasAttribute("data-label")){return}var f,g;var k=j.getAttribute("data-label");try{g=document.querySelector("template#"+k)}catch(i){}if(g){f=g.content}else{if(j.hasAttribute("data-url")){f=document.createElement("a");f.href=j.getAttribute("data-url")}else{f=document.createElement("span")}f.textContent=k}return f});Prism.hooks.add("complete",d)})();(function(){if(typeof self==="undefined"||!self.Prism||!self.document){return}if(!Prism.plugins.toolbar){console.warn("Copy to Clipboard plugin loaded before Toolbar plugin.");return}var a=window.ClipboardJS||undefined;if(!a&&typeof require==="function"){a=require("clipboard")}var b=[];Prism.plugins.toolbar.registerButton("copy-to-clipboard",function(d){var c=document.createElement("button");c.textContent="Copy";if(!a){b.push(e)}else{e()}return c;function e(){var g=new a(c,{text:function(){return d.code}});g.on("success",function(){c.textContent="Copied!";f()});g.on("error",function(){c.textContent="Press Ctrl+C to copy";f()})}function f(){setTimeout(function(){c.textContent="Copy"},5000)}})})();