{"config": {"postsPerPage": "10", "tagsPostsPerPage": 5, "authorsPostsPerPage": 5, "excerptLength": 45, "logo": "media/website/marif-logo.svg"}, "customConfig": {"pageMargin": "6vw", "pageWidth": "66rem", "entryWidth": "42rem", "borderRadius": "3", "baseline": "0.28333rem", "alignHero": "left", "titleHero": "Focussed, <br> trusted & unique", "textHero": "<p><PERSON><PERSON> is Turkey’s only boutique dispute resolution law firm. We help leading global and domestic clients navigate the often complicated issues related to solving or avoiding disputes when operating in Turkey.</p>", "heightHero": "50vh", "uploadHero": "media/website/main-banner-image.png", "uploadHeroAlt": "", "uploadHeroCaption": "", "relatedPostsNumber": "3", "teambg": "media/website/team-bg.png", "alignFeed": "left", "feedFeaturedImage": true, "feedFeaturedImageSize": "8", "feedAvatar": true, "feedAuthor": true, "feedDate": true, "feedDateType": "published", "feedtReadMore": true, "navbarHeight": "4rem", "submenu": "auto", "submenuWidth": "240", "mobilemenu": "sidebar", "mobilemenuExpandableSubmenus": true, "colorScheme": "light", "primaryColor": "#057B12", "primaryDarkColor": "#00FF21", "fontBody": "system-ui", "disableFontBodyItalic": false, "fontHeadings": "system-ui", "disableFontHeadingsItalic": false, "fontMenu": "var(--body-font)", "fontLogo": "var(--body-font)", "minFontSize": "1.1", "maxFontSize": "1.2", "lineHeight": "1.7", "letterSpacing": "0", "fontBodyWeight": "400", "fontBoldWeight": "600", "fontHeadignsWeight": "500", "fontHeadingsStyle": "normal", "fontHeadingsLineHeight": "1.2", "fontHeadingsletterSpacing": "0", "fontHeadingsTransform": "none", "shareFacebook": true, "shareTwitter": false, "shareTwitterName": "", "sharePinterest": false, "shareMix": false, "shareLinkedin": true, "shareBuffer": false, "shareWhatsApp": true, "socialButtons": true, "socialFacebook": "#", "socialTwitter": "#", "socialInstagram": "#", "socialLinkedin": "#", "socialVimeo": "#", "socialPinterest": "#", "socialYoutube": "#", "copyrightText": "<p>Powered by Publii</p>", "searchFeature": false, "createSearchPage": false, "galleryItemGap": "calc(var(--baseline) * 1.5)", "backToTopButton": true, "formatDate": "D MMM YYYY", "formatDateCustom": "HH:mm:ss YY/MM/DD", "lazyLoadEffect": "fadein", "faviconUpload": "media/website/8-1024x956.jpg", "faviconExtension": "image/x-icon", "fontBodyItalic": false, "fontHeadingsItalic": false}, "postConfig": {"displayDate": 1, "additionalImage": 1, "degignation": 1, "mobileNumber": 1, "telephoneNumber": 1, "emailAddress": 1, "linkedin": 1, "sectorsExperience": 1, "education": 1, "admission": 1, "languages": 1, "membership": 1, "category": 1, "displayAuthor": 1, "displayLastUpdatedDate": 1, "displayTags": 1, "displayShareButtons": 1, "displayAuthorBio": 1, "displayPostNavigation": 1, "displayRelatedPosts": 1, "displayComments": 0}, "pageConfig": {"displayDate": 0, "displayAuthor": 0, "displayLastUpdatedDate": 0, "displayShareButtons": 0, "displayAuthorBio": 0, "displayChildPages": 0, "displayComments": 0}, "tagConfig": {"displayFeaturedImage": 1, "displayPostCounter": 1, "displayDescription": 1, "displayPostList": 1}, "authorConfig": {"displayFeaturedImage": 1, "displayAvatar": 1, "displayPostCounter": 1, "displayDescription": 1, "displayWebsite": 1, "displayPostList": 1}, "defaultTemplates": {"post": "", "page": ""}}