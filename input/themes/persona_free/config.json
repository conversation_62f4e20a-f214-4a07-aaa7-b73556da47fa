{"name": "Persona_Free", "version": "2.1.0.0", "author": "TidyCustoms <<EMAIL>>", "menus": {"mainMenu": {"desc": "", "name": "Main menu", "maxLevels": -1}, "footerMenu": {"desc": "", "name": "Footer menu", "maxLevels": 1}}, "renderer": {"relatedPostsNumber": 4, "featuredPostsNumber": -1, "includeFeaturedInPosts": false, "renderRelatedPosts": true, "renderSimilarPosts": false, "renderPrevNextPosts": false, "createContentStructure": true, "createTagPages": true, "createAuthorPages": true, "createTagsList": true, "createSearchPage": false, "create404page": true, "customHTML": {"beforePost": "Before every post", "afterPost": "After every post", "beforePage": "Before every page", "afterPage": "After every page"}}, "supportedFeatures": {"blockEditor": true, "tagsList": true, "tagPages": true, "tagImages": true, "authorPages": true, "authorImages": false, "searchPage": true, "errorPage": true, "customSearch": true, "customSharing": true, "customComments": true, "embedConsents": true, "pages": true}, "postTemplates": {"top-image": "Featured image at the top"}, "pageTemplates": {"empty": "Empty container", "top-image": "Featured image at the top"}, "config": [{"name": "postsPerPage", "label": "Posts per page", "value": 8, "type": "number"}, {"name": "tagsPostsPerPage", "label": "Tags posts per page", "value": 8, "type": "number"}, {"name": "authorsPostsPerPage", "label": "Authors posts per page", "value": 8, "type": "number"}, {"name": "excerptLength", "label": "Excerpt length", "value": 20, "type": "number"}, {"name": "logo", "label": "Website logo", "value": "", "type": "upload", "upload": true}], "customConfig": [{"name": "pageMargin", "label": "Page margin", "group": "Layout", "value": "10vmin", "type": "text"}, {"name": "pageWidth", "label": "Page width", "group": "Layout", "value": "1440px", "type": "text"}, {"name": "entryWidth", "label": "Entry width", "group": "Layout", "value": "66ch", "type": "text"}, {"name": "separator", "type": "separator", "label": "Homepage", "group": "Layout", "size": "big"}, {"name": "textHero", "label": "Hero text", "group": "Layout", "value": "<h1>A simple, modern looking theme for your blog or portfolio website.</h1>", "type": "wysiwyg"}, {"name": "gridLayout", "label": "Layout", "group": "Layout", "value": "l-grid--2", "type": "select", "options": [{"label": "2 columns", "value": "l-grid--2"}, {"label": "3 columns", "value": "l-grid--3"}, {"label": "4 columns", "value": "l-grid--4"}]}, {"name": "filter", "group": "Layout", "label": "Show tag list", "value": true, "type": "checkbox"}, {"name": "layoutSwitcher", "group": "Layout", "label": "Show layout switcher", "value": true, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "Tag page", "group": "Layout", "size": "big"}, {"name": "gridTagLayout", "label": "Layout", "group": "Layout", "value": "l-grid--2", "type": "select", "options": [{"label": "2 columns", "value": "l-grid--2"}, {"label": "3 columns", "value": "l-grid--3"}, {"label": "4 columns", "value": "l-grid--4"}]}, {"name": "tagFilter", "group": "Layout", "label": "Show tag list", "note": "The tag list over the post listing.", "value": true, "type": "checkbox"}, {"name": "layoutTagSwitcher", "group": "Layout", "label": "Show layout switcher", "value": true, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "Tags page", "group": "Layout", "size": "medium"}, {"name": "gridTagsLayout", "label": "Layout", "group": "Layout", "value": "l-grid--2", "type": "select", "options": [{"label": "2 columns", "value": "l-grid--2"}, {"label": "3 columns", "value": "l-grid--3"}, {"label": "4 columns", "value": "l-grid--4"}]}, {"name": "layoutTagsSwitcher", "group": "Layout", "label": "Show layout switcher", "value": true, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "Author page", "group": "Layout", "size": "medium"}, {"name": "gridAuthorLayout", "label": "Layout", "group": "Layout", "value": "l-grid--2", "type": "select", "options": [{"label": "2 columns", "value": "l-grid--2"}, {"label": "3 columns", "value": "l-grid--3"}, {"label": "4 columns", "value": "l-grid--4"}]}, {"name": "layoutAuthorSwitcher", "group": "Layout", "label": "Show layout switcher", "value": true, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "Posts and Pages", "group": "Layout", "size": "medium"}, {"name": "featuredImageHeight", "label": "Featured image height", "group": "Layout", "note": "Specify the height of the featured image. Set to 'auto' to display the image in its original size.", "value": "40vmin", "type": "text"}, {"name": "relatedPostsNumber", "label": "Related post", "group": "Layout", "note": "Specify the number of related posts to show. Zero '0' means no posts.", "value": "2", "type": "text"}, {"name": "gridRelatedPostsLayout", "label": "Layout", "group": "Layout", "value": "l-grid--2", "type": "select", "options": [{"label": "2 columns", "value": "l-grid--2"}, {"label": "3 columns", "value": "l-grid--3"}, {"label": "4 columns", "value": "l-grid--4"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Post list", "note": "The Post list section offers a range of options for managing the post list, which is displayed in a visually appealing card layout on the front page, tag, and author pages. In this area, you can easily enable or disable elements such as image, main tag, or other elements.", "size": "no-line"}, {"name": "cardsImageHeight", "group": "Post list", "label": "Featured image height", "note": "Set to 'auto' to display the images in their original size", "value": "12rem", "type": "text"}, {"name": "cardsTag", "group": "Post list", "label": "Show main tag", "value": true, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "Dropdown menu", "group": "<PERSON><PERSON><PERSON>", "size": ""}, {"name": "submenu", "label": "<PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON>", "value": "auto", "type": "radio", "options": [{"label": "Auto", "value": "auto"}, {"label": "Custom", "value": "custom"}]}, {"name": "submenuWidth", "group": "<PERSON><PERSON><PERSON>", "note": "The submenu width in PX unit", "label": "", "value": "240", "type": "range", "min": 0, "max": 1000, "step": 10, "dependencies": [{"field": "submenu", "value": "custom"}]}, {"name": "separator", "type": "separator", "label": "Mobile menu", "group": "<PERSON><PERSON><PERSON>", "size": "big"}, {"name": "mobilemenuExpandableSubmenus", "label": "Expandable submenus", "group": "<PERSON><PERSON><PERSON>", "value": true, "type": "checkbox"}, {"name": "colorScheme", "label": "Select color scheme", "group": "Colors", "note": "More color schemes are available in the <a href='https://marketplace.getpublii.com/themes/persona/' target='_blank'>Premium version</a> of the Persona theme.", "value": "violet", "type": "select", "options": [{"label": "Violet", "value": "violet"}, {"label": "Custom", "value": "custom"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Colors", "size": "small", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "white", "label": "White", "group": "Colors", "value": "#FFFFFF", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "black", "label": "Black", "group": "Colors", "value": "#000000", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "green", "label": "Green", "group": "Colors", "value": "#00C899", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "blue", "label": "Blue", "group": "Colors", "value": "#3DBFE2", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "red", "label": "Red", "group": "Colors", "value": "#EB7F9B", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "yellow", "label": "Yellow", "group": "Colors", "value": "#FFC76B", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "dark", "label": "Dark", "group": "Colors", "value": "#283149", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "gray1", "label": "Gray 1", "group": "Colors", "value": "#5f5f74", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "gray2", "label": "Gray 2", "group": "Colors", "value": "#9196A2", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "light", "label": "Light", "group": "Colors", "value": "#d4d2e1", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "lighter", "label": "Lighter", "group": "Colors", "value": "#DBDDE6", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "color", "label": "Color", "group": "Colors", "value": "#6f689b", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "pageBg", "label": "Page background", "group": "Colors", "value": "linear-gradient(110deg, rgba(215,218,227,1) 0%, rgba(242,243,248,1) 100%)", "type": "textarea", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "textColor", "label": "Text color", "group": "Colors", "value": "#343435", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "headingsColor", "label": "Headings color", "group": "Colors", "value": "#283149", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "linkColor", "label": "Link color", "group": "Colors", "value": "#6f689b", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "linkColorHover", "label": "Link hover color", "group": "Colors", "value": "#283149", "type": "colorpicker", "dependencies": [{"field": "colorScheme", "value": "custom"}]}, {"name": "separator", "type": "separator", "label": "Main font settings", "group": "Fonts", "note": "To explore an extensive list of available fonts, along with detailed information about their typefaces, complete range of weights, and other specifications, please visit our <a href='https://getpublii.com/docs/fonts.html' target='_blank'>documentation</a>.", "size": "small"}, {"name": "fontBody", "label": "Body font", "group": "Fonts", "value": "system-ui", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui", "group": "System"}, {"label": "<PERSON><PERSON>", "value": "aleo", "group": "<PERSON><PERSON>"}, {"label": "Andada Pro", "value": "andadapro", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "besley", "group": "<PERSON><PERSON>"}, {"label": "Bitter", "value": "bitter", "group": "<PERSON><PERSON>"}, {"label": "Brygada 1918", "value": "brygada1918", "group": "<PERSON><PERSON>"}, {"label": "Domine", "value": "domine", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "faustina", "group": "<PERSON><PERSON>"}, {"label": "<PERSON>", "value": "frankruhllibre", "group": "<PERSON><PERSON>"}, {"label": "Imbue", "value": "imbue", "group": "<PERSON><PERSON>"}, {"label": "K<PERSON><PERSON>", "value": "kreon", "group": "<PERSON><PERSON>"}, {"label": "Labrada", "value": "labrada", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "lora", "group": "<PERSON><PERSON>"}, {"label": "Manuale", "value": "manuale", "group": "<PERSON><PERSON>"}, {"label": "Petrona", "value": "petrona", "group": "<PERSON><PERSON>"}, {"label": "Playfair Display", "value": "playfairdisplay", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON> Slab", "value": "robotoslab", "group": "<PERSON><PERSON>"}, {"label": "Rokkitt", "value": "rokkitt", "group": "<PERSON><PERSON>"}, {"label": "Yrsa", "value": "yrsa", "group": "<PERSON><PERSON>"}, {"label": "Advent Pro", "value": "adventpro", "group": "Sans Serif"}, {"label": "Antonio", "value": "antonio", "group": "Sans Serif"}, {"label": "Archivo Narrow", "value": "archivonarrow", "group": "Sans Serif"}, {"label": "Asap", "value": "asap", "group": "Sans Serif"}, {"label": "Assistant", "value": "assistant", "group": "Sans Serif"}, {"label": "Cabin", "value": "cabin", "group": "Sans Serif"}, {"label": "Cairo", "value": "cairo", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "dosis", "group": "Sans Serif"}, {"label": "Exo", "value": "exo", "group": "Sans Serif"}, {"label": "Figtree", "value": "figtree", "group": "Sans Serif"}, {"label": "Glory", "value": "glory", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "he<PERSON>o", "group": "Sans Serif"}, {"label": "Instrument Sans", "value": "instrumentsans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "jura", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "karla", "group": "Sans Serif"}, {"label": "Lexend", "value": "lexend", "group": "Sans Serif"}, {"label": "Libre Franklin", "value": "librefranklin", "group": "Sans Serif"}, {"label": "Manrope", "value": "manrope", "group": "Sans Serif"}, {"label": "Maven <PERSON>", "value": "mavenpro", "group": "Sans Serif"}, {"label": "Merriweather Sans", "value": "merriweather<PERSON>s", "group": "Sans Serif"}, {"label": "Montserrat", "value": "montserrat", "group": "Sans Serif"}, {"label": "Nunito", "value": "nunito", "group": "Sans Serif"}, {"label": "Orbitron", "value": "orbitron", "group": "Sans Serif"}, {"label": "<PERSON>", "value": "oswald", "group": "Sans Serif"}, {"label": "Plus Jakarta Sans", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "Sans Serif"}, {"label": "Public Sans", "value": "publicsans", "group": "Sans Serif"}, {"label": "Quicksand", "value": "quicksand", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "raleway", "group": "Sans Serif"}, {"label": "Red Hat Display", "value": "redhatdisplay", "group": "Sans Serif"}, {"label": "Roboto Flex", "value": "robotoflex", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "rubik", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "ruda", "group": "Sans Serif"}, {"label": "Smooch Sans", "value": "smoochsans", "group": "Sans Serif"}, {"label": "Spartan", "value": "spartan", "group": "Sans Serif"}, {"label": "Urbanist", "value": "urbanist", "group": "Sans Serif"}, {"label": "Work Sans", "value": "worksans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "yanonekaffeesatz", "group": "Sans Serif"}, {"label": "Big Shoulders Display", "value": "bigshouldersdisplay", "group": "Cursive"}, {"label": "Comfortaa", "value": "comfortaa", "group": "Cursive"}, {"label": "<PERSON>ript", "value": "dancingscript", "group": "Cursive"}, {"label": "Gluten", "value": "gluten", "group": "Cursive"}, {"label": "Lemonada", "value": "lemonada", "group": "Cursive"}, {"label": "JetBrains Mono", "value": "jetbrainsmono", "group": "Monospace"}, {"label": "Red Hat Mono", "value": "redhatmon<PERSON>", "group": "Monospace"}, {"label": "Source Code Pro", "value": "sourcecodepro", "group": "Monospace"}]}, {"name": "disableFontBodyItalic", "label": "Disable italic style", "group": "Fonts", "note": "This option allows you to disable the loading of the dedicated italic version for the body font. The italic font is automatically loaded when selecting a body font that supports it, like Lora, for optimal appearance. However, for performance reasons, you can choose to prevent loading the dedicated italic version by enabling this option.", "type": "checkbox", "value": false, "dependencies": [{"field": "fontBody", "value": "adventpro,aleo,andadapro,archivonarrow,asap,besley,bitter,brygada1918,cabin,exo,faustina,figtree,glory,instrumentsans,jetbrainsmono,karla,labrada,librefranklin,lora,manuale,merriweathersans,montserrat,nunito,petrona,playfairdisplay,plusjakartasans,publicsans,raleway,redhatdisplay,redhatmono,rokkitt,rubik,sourcecodepro,urbanist,worksans,yrsa"}]}, {"name": "fontHeadings", "label": "Headings font (H1-H6)", "group": "Fonts", "value": "system-ui", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui", "group": "System"}, {"label": "<PERSON><PERSON>", "value": "aleo", "group": "<PERSON><PERSON>"}, {"label": "Andada Pro", "value": "andadapro", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "besley", "group": "<PERSON><PERSON>"}, {"label": "Bitter", "value": "bitter", "group": "<PERSON><PERSON>"}, {"label": "Brygada 1918", "value": "brygada1918", "group": "<PERSON><PERSON>"}, {"label": "Domine", "value": "domine", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "faustina", "group": "<PERSON><PERSON>"}, {"label": "<PERSON>", "value": "frankruhllibre", "group": "<PERSON><PERSON>"}, {"label": "Imbue", "value": "imbue", "group": "<PERSON><PERSON>"}, {"label": "K<PERSON><PERSON>", "value": "kreon", "group": "<PERSON><PERSON>"}, {"label": "Labrada", "value": "labrada", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "lora", "group": "<PERSON><PERSON>"}, {"label": "Manuale", "value": "manuale", "group": "<PERSON><PERSON>"}, {"label": "Petrona", "value": "petrona", "group": "<PERSON><PERSON>"}, {"label": "Playfair Display", "value": "playfairdisplay", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON> Slab", "value": "robotoslab", "group": "<PERSON><PERSON>"}, {"label": "Rokkitt", "value": "rokkitt", "group": "<PERSON><PERSON>"}, {"label": "Yrsa", "value": "yrsa", "group": "<PERSON><PERSON>"}, {"label": "Advent Pro", "value": "adventpro", "group": "Sans Serif"}, {"label": "Antonio", "value": "antonio", "group": "Sans Serif"}, {"label": "Archivo Narrow", "value": "archivonarrow", "group": "Sans Serif"}, {"label": "Asap", "value": "asap", "group": "Sans Serif"}, {"label": "Assistant", "value": "assistant", "group": "Sans Serif"}, {"label": "Cabin", "value": "cabin", "group": "Sans Serif"}, {"label": "Cairo", "value": "cairo", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "dosis", "group": "Sans Serif"}, {"label": "Exo", "value": "exo", "group": "Sans Serif"}, {"label": "Figtree", "value": "figtree", "group": "Sans Serif"}, {"label": "Glory", "value": "glory", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "he<PERSON>o", "group": "Sans Serif"}, {"label": "Instrument Sans", "value": "instrumentsans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "jura", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "karla", "group": "Sans Serif"}, {"label": "Lexend", "value": "lexend", "group": "Sans Serif"}, {"label": "Libre Franklin", "value": "librefranklin", "group": "Sans Serif"}, {"label": "Manrope", "value": "manrope", "group": "Sans Serif"}, {"label": "Maven <PERSON>", "value": "mavenpro", "group": "Sans Serif"}, {"label": "Merriweather Sans", "value": "merriweather<PERSON>s", "group": "Sans Serif"}, {"label": "Montserrat", "value": "montserrat", "group": "Sans Serif"}, {"label": "Nunito", "value": "nunito", "group": "Sans Serif"}, {"label": "Orbitron", "value": "orbitron", "group": "Sans Serif"}, {"label": "<PERSON>", "value": "oswald", "group": "Sans Serif"}, {"label": "Plus Jakarta Sans", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "Sans Serif"}, {"label": "Public Sans", "value": "publicsans", "group": "Sans Serif"}, {"label": "Quicksand", "value": "quicksand", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "raleway", "group": "Sans Serif"}, {"label": "Red Hat Display", "value": "redhatdisplay", "group": "Sans Serif"}, {"label": "Roboto Flex", "value": "robotoflex", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "rubik", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "ruda", "group": "Sans Serif"}, {"label": "Smooch Sans", "value": "smoochsans", "group": "Sans Serif"}, {"label": "Spartan", "value": "spartan", "group": "Sans Serif"}, {"label": "Urbanist", "value": "urbanist", "group": "Sans Serif"}, {"label": "Work Sans", "value": "worksans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "yanonekaffeesatz", "group": "Sans Serif"}, {"label": "Big Shoulders Display", "value": "bigshouldersdisplay", "group": "Cursive"}, {"label": "Comfortaa", "value": "comfortaa", "group": "Cursive"}, {"label": "<PERSON>ript", "value": "dancingscript", "group": "Cursive"}, {"label": "Gluten", "value": "gluten", "group": "Cursive"}, {"label": "Lemonada", "value": "lemonada", "group": "Cursive"}, {"label": "JetBrains Mono", "value": "jetbrainsmono", "group": "Monospace"}, {"label": "Red Hat Mono", "value": "redhatmon<PERSON>", "group": "Monospace"}, {"label": "Source Code Pro", "value": "sourcecodepro", "group": "Monospace"}]}, {"name": "disableFontHeadingsItalic", "label": "Disable italic style", "group": "Fonts", "note": "This option allows you to disable loading of the dedicated italic version for the headings font. The italic font is automatically loaded when selecting a headings font that supports it, like <PERSON>ra, for optimal appearance. However, for performance reasons, you can choose to prevent loading of the dedicated italic version by enabling this option.", "type": "checkbox", "value": false, "dependencies": [{"field": "fontHeadings", "value": "adventpro,aleo,andadapro,archivonarrow,asap,besley,bitter,brygada1918,cabin,exo,faustina,figtree,glory,instrumentsans,jetbrainsmono,karla,labrada,librefranklin,lora,manuale,merriweathersans,montserrat,nunito,petrona,playfairdisplay,plusjakartasans,publicsans,raleway,redhatdisplay,redhatmono,rokkitt,rubik,sourcecodepro,urbanist,worksans,yrsa"}]}, {"name": "fontMenu", "label": "Menu font", "group": "Fonts", "value": "var(--font-body)", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui"}, {"label": "Select the font used for the Body", "value": "var(--font-body)"}, {"label": "Select the font used for the Headings", "value": "var(--font-heading)"}]}, {"name": "fontLogo", "label": "Logo font", "group": "Fonts", "value": "var(--font-body)", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui"}, {"label": "Select the font used for the Body", "value": "var(--font-body)"}, {"label": "Select the font used for the Headings", "value": "var(--font-heading)"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Fonts", "size": "big"}, {"name": "minFontSize", "label": "Minimum font size", "group": "Fonts", "note": "The font-size of the root element in REM unit", "value": "1", "type": "range", "min": 1, "max": 3, "step": 0.05}, {"name": "maxFontSize", "label": "Maximum font size", "group": "Fonts", "note": "The font-size of the root element in REM unit", "value": "1.15", "type": "range", "min": 1, "max": 3, "step": 0.05}, {"name": "lineHeight", "label": "Line height", "group": "Fonts", "note": "The default line-height for the entire website", "value": "1.6", "type": "range", "min": 1, "max": 3, "step": 0.1}, {"name": "letterSpacing", "label": "Letter spacing", "group": "Fonts", "note": "Adjusts the spacing between characters for body text elements, excluding headings, in EM.", "value": "0", "type": "range", "min": -1, "max": 1, "step": 0.01}, {"name": "separator", "type": "separator", "label": "", "group": "Fonts", "note": "Note that not all fonts support the full range of weights; instead, they will typically support a standard range between 400 and 700. To see the exact range available for the selected body font, please visit our <a href='https://getpublii.com/docs/fonts.html' target='_blank'>documentation</a>.", "size": "small thin"}, {"name": "fontBodyWeight", "label": "Normal font weight", "group": "Fonts", "value": "400", "type": "range", "min": 100, "max": 900, "step": 1}, {"name": "fontBoldWeight", "label": "Bold font weight", "group": "Fonts", "value": "700", "type": "range", "min": 100, "max": 900, "step": 1}, {"name": "separator", "type": "separator", "label": "Headings", "group": "Fonts", "size": "big"}, {"name": "fontHeadignsWeight", "label": "H1-H6 font weight", "group": "Fonts", "value": "700", "type": "range", "min": 100, "max": 900, "step": 1}, {"name": "fontHeadingsStyle", "label": "H1-H6 font style", "group": "Fonts", "value": "normal", "type": "select", "options": [{"label": "Normal", "value": "normal"}, {"label": "Italic", "value": "italic"}, {"label": "Oblique", "value": "oblique"}]}, {"name": "fontHeadingsLineHeight", "label": "H1-H6 line height", "group": "Fonts", "note": "The default line-height for heading elements (h1, h2, h3, etc.).", "value": "1.2", "type": "range", "min": 1, "max": 3, "step": 0.05}, {"name": "fontHeadingsletterSpacing", "label": "H1-H6 letter spacing", "group": "Fonts", "note": "Adjusts the spacing between characters for heading elements (h1, h2, h3, etc.) in EM.", "value": "-0.03", "type": "range", "min": -1, "max": 1, "step": 0.01}, {"name": "fontHeadingsTransform", "label": "H1-H6 text transform", "group": "Fonts", "value": "none", "type": "select", "options": [{"label": "None", "value": "none"}, {"label": "Capitalize", "value": "capitalize"}, {"label": "Lowercase", "value": "lowercase"}, {"label": "Uppercase", "value": "uppercase"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Share Buttons", "note": "This section allows you to choose your preferred share buttons for various platforms. If you wish to add more, you can install the <a href='https://marketplace.getpublii.com/plugins/social-sharing/' target='_blank'>Social Sharing plugin</a> available on the Publii Marketplace.", "size": "no-line"}, {"name": "shareFacebook", "group": "Share Buttons", "label": "Facebook", "value": false, "type": "checkbox"}, {"name": "shareTwitter", "group": "Share Buttons", "label": "X (formerly Twitter)", "value": false, "type": "checkbox"}, {"name": "shareTwitterName", "group": "Share Buttons", "note": "Enter your X account's handle here; it will be used when the X share button is clicked on your site e.g. 'via @XHandle'. If left blank, the default username of @SiteName will be used", "value": "", "type": "text", "dependencies": [{"field": "shareTwitter", "value": "true"}]}, {"name": "sharePinterest", "group": "Share Buttons", "label": "Pinterest", "value": false, "type": "checkbox"}, {"name": "shareMix", "group": "Share Buttons", "label": "Mix (formerly StumbleUpon)", "value": false, "type": "checkbox"}, {"name": "shareLinkedin", "group": "Share Buttons", "label": "LinkedIn", "value": false, "type": "checkbox"}, {"name": "shareBuffer", "group": "Share Buttons", "label": "<PERSON><PERSON><PERSON>", "value": false, "type": "checkbox"}, {"name": "shareWhatsApp", "group": "Share Buttons", "label": "WhatsApp", "value": false, "type": "checkbox"}, {"name": "sharePocket", "group": "Share Buttons", "label": "Pocket", "value": false, "type": "checkbox"}, {"name": "paginationType", "label": "Pagination type", "group": "Pagination", "note": "More pagination options, such as the Infinite Scroll, are available in the <a href='https://marketplace.getpublii.com/themes/persona/' target='_blank'>Premium version</a> of the Persona theme.", "value": "default", "type": "select", "options": [{"label": "<PERSON><PERSON><PERSON>", "value": "default"}]}, {"name": "newsletter", "label": "Newsletter", "group": "Newsletter", "value": "disabled", "type": "select", "options": [{"label": "MailChimp", "value": "mailchimp"}, {"label": "Custom", "value": "custom"}, {"label": "Disabled", "value": "disabled"}]}, {"name": "newsletterMailchimpURL", "label": "Form action URL", "group": "Newsletter", "placeholder": "https://site.us1.list-manage.com/subscribe/post?u=f7115d04c869d1234cc6ab1f8&amp;id=dbe56f9562", "note": "Find your  <a href=\"https://getpublii.com/docs/mailchimp-form-integration.html\" target=\"_blank\">form action URL</a>", "value": "", "type": "text", "dependencies": [{"field": "newsletter", "value": "mailchimp"}]}, {"name": "newsletterFormCode", "group": "Newsletter", "label": "", "note": "Before pasting your code, learn how to make your newsletter form as functional and visually-appealing as possible. <a href=\"https://getpublii.com/docs/make-newsletter-form-functional-and-visually-appealing.html\" target=\"_blank\">Learn more</a>", "value": "<form>...</form>", "type": "textarea", "rows": 10, "dependencies": [{"field": "newsletter", "value": "custom"}]}, {"name": "copyrightText", "label": "Copyright text", "group": "Footer", "value": "Powered by <PERSON><PERSON><PERSON>", "type": "textarea"}, {"name": "separator", "type": "separator", "label": "", "group": "Footer", "note": "This section allows you to add follow buttons for various social media platforms. If you want to expand the available options, you can install the <a href='https://marketplace.getpublii.com/plugins/follow-buttons/' target='_blank'>Follow Buttons plugin</a> available on the Publii Marketplace.", "size": "small thin"}, {"name": "socialButtons", "group": "Footer", "label": "Follow <PERSON><PERSON>", "value": false, "type": "checkbox"}, {"name": "socialFacebook", "label": "Facebook", "group": "Footer", "placeholder": "Please enter your Facebook URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialTwitter", "label": "X (formerly Twitter)", "group": "Footer", "placeholder": "Please enter your X URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialInstagram", "label": "Instagram", "group": "Footer", "placeholder": "Please enter your Instagram URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialLinkedin", "label": "LinkedIn", "group": "Footer", "placeholder": "Please enter your LinkedIn URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialVimeo", "label": "Vimeo", "group": "Footer", "placeholder": "Please enter your Vimeo URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialPinterest", "label": "Pinterest", "group": "Footer", "placeholder": "Please enter your Pinterest URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialYoutube", "label": "Youtube", "group": "Footer", "placeholder": "Please enter your Youtube URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "searchFeature", "label": "Search", "group": "Search", "note": "Enable / disable the search box, which is usually located next to the top menu. In order to use the search function, you also need to install the search plugin.", "value": false, "type": "checkbox"}, {"name": "createSearchPage", "label": "Search subpage", "group": "Search", "note": "Enabling this option will create an additional search.html page which may be required by some search plugins.", "value": false, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "", "group": "Gallery", "note": "To use the gallery functionality, you need to install the appropriate plugin. Please check the available gallery plugins in the <a rel=\"noopener noreferrer\" href=\"https://marketplace.getpublii.com/plugins/\" target=\"_blank\">Publii Marketplace</a>.", "size": "no-line"}, {"name": "galleryItemGap", "label": "Gallery item spacing", "group": "Gallery", "note": "Use of REM units recommended but you can also use others (px, vw,)", "value": "0.5rem", "type": "text"}, {"name": "animatedLines", "group": "Additional", "label": "Animated background", "note": "Once enabled, vertical animated lines will be displayed on the page also when loading images, but only if the Lazyload option is enabled in Site Settings.", "value": true, "type": "checkbox"}, {"name": "animatedLinesDuration", "label": "Animation duration", "group": "Additional", "note": "in seconds", "value": "8", "type": "range", "min": 0, "max": 30, "step": 1}, {"name": "separator", "type": "separator", "label": "", "group": "Additional", "size": "small"}, {"name": "backToTopButton", "group": "Additional", "label": "\"Back to top\" button", "value": true, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "", "group": "Additional", "size": "small"}, {"name": "formatDate", "label": "Date format", "group": "Additional", "value": "MMMM D, YYYY", "type": "select", "options": [{"label": "Nov 1, 2016", "value": "MMM D, YYYY"}, {"label": "1 Nov 2016", "value": "D MMM YYYY"}, {"label": "November 1, 2016", "value": "MMMM D, YYYY"}, {"label": "1 November 2016", "value": "D MMMM YYYY"}, {"label": "Sunday, November 1, 2016", "value": "dddd, MMMM D, YYYY"}, {"label": "Sunday, 1 November 2016", "value": "dddd, D MMMM YYYY"}, {"label": "November 1, 2016 10:02:05", "value": "MMMM D, YYYY HH:mm:ss"}, {"label": "1 November 2016 10:02:05", "value": "D MMMM YYYY HH:mm:ss"}, {"label": "01/21/2016", "value": "MM/DD/YYYY"}, {"label": "21/01/2016", "value": "DD/MM/YYYY"}, {"label": "Custom - create your own date format", "value": "custom"}]}, {"name": "formatDateCustom", "group": "Additional", "label": "Create a custom date format", "note": "More details you can find <a href='https://github.com/taylorhakes/fecha' target='_blank'>here.</a>", "value": "HH:mm:ss YY/MM/DD", "type": "text", "dependencies": [{"field": "formatDate", "value": "custom"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Additional", "size": "small"}, {"name": "lazyLoadEffect", "label": "Lazy load effects", "group": "Additional", "note": "This option works only if the 'Media lazy load' option is enabled in the Website Speed tab under Site Settings", "value": "fadein", "type": "select", "options": [{"label": "Fade in", "value": "fadein"}, {"label": "None", "value": "none"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Additional", "size": "small"}, {"name": "faviconUpload", "label": "Upload favicon file", "group": "Additional", "note": "The ideal size of a favicon is 16x16 pixels. Save your favicon as either favicon.png or favicon.ico", "value": "", "type": "smallupload", "upload": true}, {"name": "faviconExtension", "label": "Favicon extension", "group": "Additional", "value": "image/x-icon", "type": "select", "options": [{"label": ".ico", "value": "image/x-icon"}, {"label": ".png", "value": "image/png"}]}], "postConfig": [{"name": "featuredImageWidth", "label": "Featured image width", "value": "default", "type": "select", "options": [{"label": "<PERSON><PERSON><PERSON>", "value": "default"}, {"label": "Full", "value": "full"}]}, {"name": "displayAuthor", "label": "Display author name", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayDate", "label": "Display date", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayLastUpdatedDate", "label": "Display last updated date", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayShareButtons", "label": "Display share buttons", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayTags", "label": "Display tags", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayNewsletter", "label": "Display newsletter", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayComments", "label": "Display comments", "value": 0, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayRelatedPosts", "label": "Display related posts", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "pageConfig": [{"name": "featuredImageWidth", "label": "Featured image width", "value": "default", "type": "select", "options": [{"label": "<PERSON><PERSON><PERSON>", "value": "default"}, {"label": "Full", "value": "full"}]}, {"name": "displayAuthor", "label": "Display author name", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayDate", "label": "Display date", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayLastUpdatedDate", "label": "Display last updated date", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayShareButtons", "label": "Display share buttons", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayChildPages", "label": "Display child pages", "value": 0, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayNewsletter", "label": "Display newsletter", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayComments", "label": "Display comments", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "authorConfig": [{"name": "displayAvatar", "label": "Display avatar", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostCounter", "label": "Display post counter", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayDescription", "label": "Display author biography", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayWebsite", "label": "Display author website", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostList", "label": "Display post list", "note": "Disabling the post list can cause duplicate content issues. To avoid this, please disable authors pagination in the SEO section of your site settings.", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "tagConfig": [{"name": "displayPostCounter", "label": "Display post counter", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayDescription", "label": "Display tag description", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostList", "label": "Display post list", "note": "Disabling the post list can cause duplicate content issues. To avoid this, please disable tag pagination in the SEO section of your site settings.", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "files": {"ignoreAssets": ["scss", ".DS_Store"], "assetsPath": "assets", "useDynamicAssets": true, "partialsPath": "partials", "responsiveImages": {"contentImages": {"sizes": "(min-width: 37.5em) 1600px, 80vw", "dimensions": {"xs": {"width": 384, "height": "auto"}, "sm": {"width": 600, "height": "auto"}, "md": {"width": 768, "height": "auto"}, "lg": {"width": 1200, "height": "auto"}, "xl": {"width": 1600, "height": "auto"}}}, "featuredImages": {"sizes": {"post": "(min-width: 37.5em) 1600px, 80vw", "cards-col-2": "(min-width: 37.5em) 80vw, 50vw", "cards-col-3": "(min-width: 37.5em) 8vw, 33vw", "cards-col-4": "(min-width: 37.5em) 80vw, 25vw"}, "dimensions": {"xs": {"width": 384, "height": "auto", "group": "post,cards"}, "sm": {"width": 600, "height": "auto", "group": "post,cards"}, "md": {"width": 768, "height": "auto", "group": "post,cards"}, "lg": {"width": 1200, "height": "auto", "group": "post,cards"}, "xl": {"width": 1600, "height": "auto", "group": "post"}}}, "tagImages": {"sizes": {"cards-col-2": "(min-width: 37.5em) 80vw, 50vw", "cards-col-3": "(min-width: 37.5em) 8vw, 33vw", "cards-col-4": "(min-width: 37.5em) 80vw, 25vw"}, "dimensions": {"xs": {"width": 384, "height": "auto"}, "sm": {"width": 600, "height": "auto"}, "md": {"width": 768, "height": "auto"}, "lg": {"width": 1200, "height": "auto"}}}, "optionImages": {"sizes": "(max-width: 48em) 100vw, 80vw", "dimensions": {"xs": {"width": 384, "height": "auto"}, "sm": {"width": 600, "height": "auto"}}}, "galleryImages": {"sizes": "", "dimensions": {"thumbnail": {"width": 768, "height": "auto"}}}}}, "customElements": [{"label": "Drop cap", "cssClasses": "dropcap", "selector": "p"}, {"label": "Info", "cssClasses": "msg msg--info", "selector": "p"}, {"label": "Highlight", "cssClasses": "msg msg--highlight ", "selector": "p"}, {"label": "Success", "cssClasses": "msg msg--success", "selector": "p"}, {"label": "Warning", "cssClasses": "msg msg--warning", "selector": "p"}, {"label": "Button default", "cssClasses": "btn", "selector": "a"}, {"label": "<PERSON><PERSON> white", "cssClasses": "btn btn--white", "selector": "a, button, input[type=button], input[type=submit]"}]}