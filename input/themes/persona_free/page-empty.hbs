{{> head}}
{{> navbar}}
<main class="main page">
   {{#if @customHTML.beforePage}}
      <div class="banner banner--before-content">
         {{{@customHTML.beforePage}}}
      </div>
   {{/if}}
   {{#page}}
      <div class="content">      
         <div class="content__header">
            <h1 class="content__title">
               {{title}}
            </h1>  
         </div>
         {{#featuredImage}}
            {{#if url}}
               <figure class="content__featured-image {{#checkIf @config.page.featuredImageWidth '==' "full"}} content__featured-image--full{{/checkIf}}">
                  <div class="content__featured-image__inner {{#checkIf @config.site.mediaLazyLoad '&&' @config.custom.animatedLines}}is-img-loading{{/checkIf}}">
                     <img
                        src="{{url}}"
                        {{#if @config.site.responsiveImages}}
                           {{responsiveImageAttributes 'featuredImage' srcset.post sizes.post}}
                        {{/if}}
                        {{ lazyload "eager" }}
                        height="{{height}}"
                        width="{{width}}"
                        alt="{{alt}}">
                  </div>
                  {{#checkIfAny caption credits}}
                     <figcaption>
                        {{{caption}}}
                        {{{credits}}}
                     </figcaption>
                  {{/checkIfAny}}
               </figure>
            {{/if}}
         {{/featuredImage}}   

         <div class="content__entry">
            {{{text}}}
         </div>
      </div>
  
      {{#if @config.page.displayChildPages}}
         {{#if subpages}}
            <div class="subpages">
               <h2 class="h6 subpages__title">{{ translate 'page.childPages' }}</h2>
               <ul class="subpages__list">
                  {{> subpages-list}}
               </ul>
            </div>
         {{/if}}
      {{/if}}

      {{#if @customHTML.afterPage}}
         <div class="banner banner--after-content">
            {{{@customHTML.afterPage}}}
         </div>
      {{/if}}

   {{/page}}
</main>
{{> footer}}
