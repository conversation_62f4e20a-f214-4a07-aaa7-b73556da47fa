{{#checkIf @config.custom.newsletter '!=' "disabled"}}
   <div class="newsletter">
      <div class="post__inner">
         <h3 class="newsletter__title h4">{{ translate 'partials.newsletter.title' }}</h3>
         <p class="newsletter__desc">{{ translate 'partials.newsletter.description' }} </p>

         {{#checkIf @config.custom.newsletter '===' "mailchimp"}}
            <form
               action="{{@config.custom.newsletterMailchimpURL}}"
               method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form" class="validate"
               target="_blank">
                  <input name="EMAIL" class="required email" placeholder="{{ translate 'partials.newsletter.emailAddress' }}" id="mce-EMAIL"
                     type="email" required>
                  <input value="Subscribe" name="subscribe" id="mc-embedded-subscribe" type="submit">
            </form>
         {{/checkIf}}

         {{#checkIf @config.custom.newsletter '===' "custom"}}
            {{{@config.custom.newsletterFormCode}}}
         {{/checkIf}}

      </div>
   </div>
{{/checkIf}}
