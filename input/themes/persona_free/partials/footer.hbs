   <footer class="footer">
      {{#checkIfAny menus.footerMenu @config.custom.copyrightText}}
         <div class="footer__left">
            {{#if menus.footerMenu}}
               <ul class="footer__nav">
                  {{> simple-menu menus.footerMenu}}
               </ul>
            {{/if}}
            
            {{#if @config.custom.copyrightText}}
               <div class="footer__copy">
                  {{{@config.custom.copyrightText}}}
               </div>
            {{/if}}
         </div>
      {{/checkIfAny}}
      
      {{#if @config.custom.socialButtons}}
         <div class="footer__right">
            <div class="footer__social">
               {{#if @config.custom.socialFacebook}}
                  <a
                     href="{{@config.custom.socialFacebook}}"
                     aria-label="Facebook"
                     class="facebook">
                     <svg>
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#facebook"/>
                     </svg>
                  </a>
               {{/if}}
               {{#if @config.custom.socialTwitter}}
                  <a href="{{@config.custom.socialTwitter}}" aria-label="Twitter" class="twitter">
                     <svg>
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#twitter"/>
                     </svg>
                  </a>
               {{/if}}
               {{#if @config.custom.socialInstagram}}
                  <a
                     href="{{@config.custom.socialInstagram}}"
                     aria-label="Instagram"
                     class="instagram">
                     <svg>
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#instagram"/>
                     </svg>
                  </a>
               {{/if}}
               {{#if @config.custom.socialLinkedin}}
                  <a
                     href="{{@config.custom.socialLinkedin}}"
                     aria-label="LinkedIn"
                     class="linkedin">
                     <svg>
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#linkedin"/>
                     </svg>
                  </a>
               {{/if}}
               {{#if @config.custom.socialVimeo}}
                  <a href="{{@config.custom.socialVimeo}}" aria-label="Vimeo" class="vimeo">
                     <svg>
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#vimeo"/>
                     </svg>
                  </a>
               {{/if}}
               {{#if @config.custom.socialPinterest}}
                  <a
                     href="{{@config.custom.socialPinterest}}"
                     aria-label="Pinterest"
                     class="pinterest">
                     <svg>
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#pinterest"/>
                     </svg>
                  </a>
               {{/if}}
               {{#if @config.custom.socialYoutube}}
                  <a href="{{@config.custom.socialYoutube}}" aria-label="Youtube" class="youtube">
                     <svg>
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#youtube"/>
                     </svg>
                  </a>
               {{/if}}
            </div>
         </div>
      {{/if}}

      {{#if @config.custom.backToTopButton}}
         <button
            onclick="backToTopFunction()"
            id="backToTop"
            class="footer__bttop"
            aria-label="{{ translate 'partials.footer.backToTop' }}"
            title="{{ translate 'partials.footer.backToTop' }}">
            <svg>
               <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#toparrow"/>
            </svg>
         </button>
      {{/if}}
   </footer>
</div>

<script> window.publiiThemeMenuConfig = { mobileMenuMode: 'sidebar', animationSpeed: 300, submenuWidth: {{#checkIf @config.custom.submenu '==' "auto" }}'auto'{{else}}{{@config.custom.submenuWidth}}{{/checkIf}}, doubleClickTime: 500, mobileMenuExpandableSubmenus: {{@config.custom.mobilemenuExpandableSubmenus}}, relatedContainerForOverlayMenuSelector: '.navbar', }; </script>

<script defer src="{{js "scripts.min.js"}}"></script>
{{#if @config.site.mediaLazyLoad}}
    <script>          
      function publiiDetectLoadedImages () {
         var images = document.querySelectorAll('img[loading]:not(.is-loaded)');
         for (var i = 0; i < images.length; i++) {
            if (images[i].complete) {
               images[i].classList.add('is-loaded');
               images[i].parentNode.classList.remove('is-img-loading');
            } else {
               images[i].addEventListener('load', function () {
                  this.classList.add('is-loaded');
                  this.parentNode.classList.remove('is-img-loading');
               }, false);
            }
         }
      }
      publiiDetectLoadedImages();
    </script>
{{/if}}

{{#if @renderer.previewMode}}
   <script defer src="{{js "svg-map.js"}}"></script>
   <script defer src="{{js "svg-fix.js"}}"></script>
{{/if}}
{{{@footerCustomCode}}}
{{{ publiiFooter }}}
</body>
</html>
