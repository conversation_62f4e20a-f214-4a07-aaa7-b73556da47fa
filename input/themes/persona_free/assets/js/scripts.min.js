!function(e){var t={mobileMenuMode:"overlay",animationSpeed:300,submenuWidth:300,doubleClickTime:500,mobileMenuExpandableSubmenus:!1,isHoverMenu:!0,wrapperSelector:".navbar",buttonSelector:".navbar__toggle",menuSelector:".navbar__menu",submenuSelector:".navbar__submenu",mobileMenuSidebarLogoSelector:null,mobileMenuSidebarLogoUrl:null,relatedContainerForOverlayMenuSelector:null,ariaButtonAttribute:"aria-haspopup",separatorItemClass:"is-separator",parentItemClass:"has-submenu",submenuLeftPositionClass:"is-left-submenu",submenuRightPositionClass:"is-right-submenu",mobileMenuOverlayClass:"navbar_mobile_overlay",mobileMenuSubmenuWrapperClass:"navbar__submenu_wrapper",mobileMenuSidebarClass:"navbar_mobile_sidebar",mobileMenuSidebarOverlayClass:"navbar_mobile_sidebar__overlay",hiddenElementClass:"is-hidden",openedMenuClass:"is-active",noScrollClass:"no-scroll",relatedContainerForOverlayMenuClass:"is-visible"},s={};function n(e){for(var t=e.querySelectorAll(s.submenuSelector),n=0;n<t.length;n++)t[n].setAttribute("aria-hidden",!1)}function a(e){for(var t=e.querySelectorAll(s.submenuSelector),n=0;n<t.length;n++){var a=document.createElement("div");a.classList.add(s.mobileMenuSubmenuWrapperClass),t[n].parentNode.insertBefore(a,t[n]),a.appendChild(t[n])}}function o(e){for(var t=e.querySelectorAll("."+s.parentItemClass),n=0;n<t.length;n++){t[n].addEventListener("click",(function(e){e.stopPropagation();var t=this.querySelector("."+s.mobileMenuSubmenuWrapperClass),n=t.firstElementChild;if(t.classList.contains(s.openedMenuClass)){var a=n.clientHeight;t.style.height=a+"px",setTimeout((function(){t.style.height="0px"}),0),setTimeout((function(){t.removeAttribute("style"),t.classList.remove(s.openedMenuClass)}),s.animationSpeed),n.setAttribute("aria-hidden",!0),n.parentNode.firstElementChild.setAttribute("aria-expanded",!1)}else{a=n.clientHeight;t.classList.add(s.openedMenuClass),t.style.height="0px",setTimeout((function(){t.style.height=a+"px"}),0),setTimeout((function(){t.removeAttribute("style")}),s.animationSpeed),n.setAttribute("aria-hidden",!1),n.parentNode.firstElementChild.setAttribute("aria-expanded",!0)}}));for(var a=t[n].children,o=0;o<a.length;o++)"A"===a[o].tagName&&a[o].addEventListener("click",(function(e){var t=parseInt(this.getAttribute("data-last-click"),10),n=+new Date;isNaN(t)||t+s.doubleClickTime<=n?(e.preventDefault(),this.setAttribute("data-last-click",n)):t+s.doubleClickTime>n&&(e.stopPropagation(),i(this,!0))}))}}function i(e,t){if(!1!==t||!e.parentNode.classList.contains(s.parentItemClass)){var n=document.querySelector(s.relatedContainerForOverlayMenuSelector),a=document.querySelector(s.buttonSelector),o=document.querySelector("."+s.mobileMenuOverlayClass);o||(o=document.querySelector("."+s.mobileMenuSidebarClass)),o.classList.add(s.hiddenElementClass),a.classList.remove(s.openedMenuClass),a.setAttribute(s.ariaButtonAttribute,!1),document.documentElement.classList.remove(s.noScrollClass),n&&n.classList.remove(s.relatedContainerForOverlayMenuClass);var i=document.querySelector("."+s.mobileMenuSidebarOverlayClass);i&&i.classList.add(s.hiddenElementClass)}}Object.keys(t).forEach((function(e){s[e]=t[e]})),"object"==typeof e&&Object.keys(e).forEach((function(t){s[t]=e[t]})),document.querySelectorAll(s.wrapperSelector).length&&(function(){for(var e=document.querySelectorAll(s.wrapperSelector+" ."+s.parentItemClass),t=0;t<e.length;t++){var n=s.isHoverMenu?"mouseenter":"click";e[t].addEventListener(n,(function(){var e=this.querySelector(s.submenuSelector),t=this.getBoundingClientRect().left,n=2;if(this.parentNode===document.querySelector(s.menuSelector)&&(n=1),"auto"!==s.submenuWidth){var a=t+s.submenuWidth*n;window.innerWidth<a?(e.classList.remove(s.submenuLeftPositionClass),e.classList.add(s.submenuRightPositionClass)):(e.classList.remove(s.submenuRightPositionClass),e.classList.add(s.submenuLeftPositionClass))}else{a=0;var o=0;a=1===n?t+e.clientWidth:t+this.clientWidth+e.clientWidth,window.innerWidth<a?(e.classList.remove(s.submenuLeftPositionClass),e.classList.add(s.submenuRightPositionClass),o=-1*e.clientWidth,e.removeAttribute("style"),1===n?(o=0,e.style.right=o+"px"):e.style.right=this.clientWidth+"px"):(e.classList.remove(s.submenuRightPositionClass),e.classList.add(s.submenuLeftPositionClass),o=this.clientWidth,1===n&&(o=0),e.removeAttribute("style"),e.style.left=o+"px")}e.setAttribute("aria-hidden",!1)})),s.isHoverMenu&&e[t].addEventListener("mouseleave",(function(){var e=this.querySelector(s.submenuSelector);e.removeAttribute("style"),e.setAttribute("aria-hidden",!0)}))}}(),"overlay"===s.mobileMenuMode?function(){var e=document.createElement("div");e.classList.add(s.mobileMenuOverlayClass),e.classList.add(s.hiddenElementClass);var t=document.querySelector(s.menuSelector).outerHTML;e.innerHTML=t,document.body.appendChild(e),s.mobileMenuExpandableSubmenus?(a(e),o(e)):n(e);var i=document.querySelector(s.buttonSelector);i.addEventListener("click",(function(){var t=document.querySelector(s.relatedContainerForOverlayMenuSelector);e.classList.toggle(s.hiddenElementClass),i.classList.toggle(s.openedMenuClass),i.setAttribute(s.ariaButtonAttribute,i.classList.contains(s.openedMenuClass)),i.classList.contains(s.openedMenuClass)?(document.documentElement.classList.add(s.noScrollClass),t&&t.classList.add(s.relatedContainerForOverlayMenuClass)):(document.documentElement.classList.remove(s.noScrollClass),t&&t.classList.remove(s.relatedContainerForOverlayMenuClass))}))}():"sidebar"===s.mobileMenuMode&&function(){var e=document.createElement("div");e.classList.add(s.mobileMenuSidebarClass),e.classList.add(s.hiddenElementClass);var t="";null!==s.mobileMenuSidebarLogoSelector?t=document.querySelector(s.mobileMenuSidebarLogoSelector).outerHTML:null!==s.mobileMenuSidebarLogoUrl&&(t='<img src="'+s.mobileMenuSidebarLogoUrl+'" alt="" />'),t+=document.querySelector(s.menuSelector).outerHTML,e.innerHTML=t;var i=document.createElement("div");i.classList.add(s.mobileMenuSidebarOverlayClass),i.classList.add(s.hiddenElementClass),document.body.appendChild(i),document.body.appendChild(e),s.mobileMenuExpandableSubmenus?(a(e),o(e)):n(e),e.addEventListener("click",(function(e){e.stopPropagation()})),i.addEventListener("click",(function(){e.classList.add(s.hiddenElementClass),i.classList.add(s.hiddenElementClass),l.classList.remove(s.openedMenuClass),l.setAttribute(s.ariaButtonAttribute,!1),document.documentElement.classList.remove(s.noScrollClass)}));var l=document.querySelector(s.buttonSelector);l.addEventListener("click",(function(){e.classList.toggle(s.hiddenElementClass),i.classList.toggle(s.hiddenElementClass),l.classList.toggle(s.openedMenuClass),l.setAttribute(s.ariaButtonAttribute,l.classList.contains(s.openedMenuClass)),document.documentElement.classList.toggle(s.noScrollClass)}))}(),function(){for(var e=document.querySelectorAll(s.menuSelector+" a"),t=0;t<e.length;t++)e[t].parentNode.classList.contains(s.parentItemClass)||e[t].addEventListener("click",(function(e){i(this,!1)}))}(),s.isHoverMenu||function(){for(var e=document.querySelectorAll(s.wrapperSelector+" *[aria-hidden]"),t=0;t<e.length;t++){var n=e[t];n.parentNode.classList.contains("active")||n.parentNode.classList.contains("active-parent")?(n.setAttribute("aria-hidden","false"),n.parentNode.firstElementChild.setAttribute("aria-expanded",!0)):(n.setAttribute("aria-hidden","true"),n.parentNode.firstElementChild.setAttribute("aria-expanded",!1))}}())}(window.publiiThemeMenuConfig),function(){const e=document.querySelector(".js-content__share-button"),t=document.querySelector(".js-content__share-popup");e&&t&&(t.addEventListener("click",(function(e){e.stopPropagation()})),e.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation(),t.classList.toggle("is-visible")})),document.body.addEventListener("click",(function(){t.classList.remove("is-visible")})));const s=".js-share",n=500,a=500;function o(e){e.preventDefault();const o=e.target.closest(s);if(!o)return;t&&t.classList.remove("is-visible");const i=Math.floor((window.innerWidth-n)/2),l=Math.floor((window.innerHeight-a)/2),r=o.href,c=window.open(r,"social",`\n\t\t\t  width=${n},\n\t\t\t  height=${a},\n\t\t\t  left=${i},\n\t\t\t  top=${l},\n\t\t\t  location=0,\n\t\t\t  menubar=0,\n\t\t\t  toolbar=0,\n\t\t\t  status=0,\n\t\t\t  scrollbars=1,\n\t\t\t  resizable=1\n\t\t `);c&&c.focus()}document.querySelectorAll(s).forEach((e=>{e.addEventListener("click",o)}))}();const backToTopButton=document.getElementById("backToTop");backToTopButton&&(window.addEventListener("scroll",(()=>{document.body.scrollTop>400||document.documentElement.scrollTop>400?backToTopButton.classList.add("is-visible"):backToTopButton.classList.remove("is-visible")})),backToTopButton.addEventListener("click",(()=>{window.scrollTo({top:0,behavior:"smooth"})}))),function(){let e=document.querySelectorAll(".post__video, .post__iframe");for(let t=0;t<e.length;t++){let s=e[t].querySelector("iframe, embed, video, object");if(!s)continue;if("false"===s.getAttribute("data-responsive"))continue;let n=s.getAttribute("width"),a=s.getAttribute("height"),o=!1;if(n&&a&&(n.indexOf("%")>-1&&a.indexOf("%")>-1?(n=parseFloat(n.replace("%","")),a=parseFloat(a.replace("%","")),o=a/n):-1===n.indexOf("%")&&-1===a.indexOf("%")&&(n=parseInt(n,10),a=parseInt(a,10),o=a/n),!1!==o)){let s=100*o+"%";e[t].setAttribute("style","--embed-aspect-ratio:"+s)}}}();const searchButton=document.querySelector(".js-search-btn"),searchOverlay=document.querySelector(".js-search-overlay"),searchInput=document.querySelector(".search__input");searchButton&&searchOverlay&&(searchButton.addEventListener("click",(e=>{e.stopPropagation(),searchOverlay.classList.toggle("expanded"),searchInput&&setTimeout((()=>{searchOverlay.classList.contains("expanded")&&searchInput.focus()}),60)})),searchOverlay.addEventListener("click",(e=>{e.stopPropagation()})),document.body.addEventListener("click",(()=>{searchOverlay.classList.remove("expanded")}))),function(){let e,t=document.querySelectorAll(".switchers__item"),s=document.querySelector(".l-grid");if(s&&(e=s.querySelectorAll(".c-card")),!s||!t.length||e&&!e.length)return;let n=t[0],a=t[1];function o(){n.classList.add("is-active"),a.classList.remove("is-active"),s.classList.remove("l-grid--1"),e=s.querySelectorAll(".c-card");for(let t=0;t<e.length;t++)e[t].classList.remove("c-card--rows");localStorage.setItem("persona-theme-selected-layout","grid"),l()}function i(){n.classList.remove("is-active"),a.classList.add("is-active"),s.classList.add("l-grid--1"),e=s.querySelectorAll(".c-card");for(let t=0;t<e.length;t++)e[t].classList.add("c-card--rows");localStorage.setItem("persona-theme-selected-layout","rows"),l()}function l(){window.personaThemeIsotopeInstance&&(document.querySelector(".filter__item.is-active")?window.personaThemeIsotopeInstance.arrange({filter:document.querySelector(".filter__item.is-active").getAttribute("data-filter")}):window.personaThemeIsotopeInstance.arrange())}n.addEventListener("click",(e=>{e.preventDefault(),n.classList.contains("is-active")||o()})),a.addEventListener("click",(e=>{e.preventDefault(),a.classList.contains("is-active")||i()})),window.addEventListener("DOMContentLoaded",(function(){setTimeout((()=>{if(localStorage.getItem("persona-theme-selected-layout")){let e=localStorage.getItem("persona-theme-selected-layout");"grid"===e&&o(),"rows"===e&&i()}}),0)}),!1)}();