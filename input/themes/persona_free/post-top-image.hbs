{{> head}}
{{> navbar}}
<main class="main post">
   {{#if @customHTML.beforePost}}
      <div class="banner banner--before-content">
         {{{@customHTML.beforePost }}}
      </div>
   {{/if}}
   {{#post}}
      <article class="content">  
         {{#featuredImage}}
            {{#if url}}
               <figure class="content__featured-image {{#checkIf @config.post.featuredImageWidth '==' "full"}} content__featured-image--full{{/checkIf}}">
                  <div class="content__featured-image__inner is-img-loading">
                     <img
                        src="{{url}}"
                        {{#if @config.site.responsiveImages}}
                           {{responsiveImageAttributes 'featuredImage' srcset.post sizes.post}}
                        {{/if}}
                        {{ lazyload "eager" }}
                        height="{{height}}"
                        width="{{width}}"
                        alt="{{alt}}">
                  </div>
                  {{#checkIfAny caption credits}}
                     <figcaption>
                        {{{caption}}}
                        {{{credits}}}
                     </figcaption>
                  {{/checkIfAny}}
               </figure>
            {{/if}}
         {{/featuredImage}} 

         <header class="content__inner content__header">
            <h1 class="content__title">
               {{title}}
            </h1>
            {{#checkIfAny @config.post.displayAuthor @config.post.displayDate @config.post.displayLastUpdatedDate}}
               <div class="content__meta">
                  {{#if @config.post.displayAuthor}}
                     <div class="content__meta__left">                        
                        {{#author}}
                           <a href="{{url}}" class="invert content__author" rel="author" title="{{name}}">
                              {{#if avatar}}
                                 <img 
                                    src="{{avatarImage.url}}" 
                                    {{ lazyload "eager" }}
                                    height="{{avatarImage.height}}"
                                    width="{{avatarImage.width}}"
                                    alt="{{avatarImage.alt}}">
                              {{/if}}
                              {{name}}
                           </a>
                        {{/author}}                   
                     </div>
                  {{/if}}

                  {{#checkIfAny @config.post.displayDate @config.post.displayLastUpdatedDate}}
                     <div class="content__meta__right">
                        {{#if @config.post.displayDate}}
                           <time datetime="{{date createdAt 'YYYY-MM-DDTHH:mm'}}" class="content__date">
                              {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                 {{date createdAt @config.custom.formatDate}}
                              {{else}}
                                 {{date createdAt @config.custom.formatDateCustom}}
                              {{/checkIf}}
                           </time>
                        {{/if}}

                        {{#if @config.post.displayLastUpdatedDate}}
                           {{#if modifiedAt}}
                              <div class="content__updated">
                                 {{ translate 'post.updatedOn' }}
                                 <time datetime="{{date createdAt 'YYYY-MM-DDTHH:mm'}}" class="content__date">
                                    {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                       {{date modifiedAt @config.custom.formatDate}}
                                    {{else}}
                                       {{date modifiedAt @config.custom.formatDateCustom}}
                                    {{/checkIf}}
                                 </time>
                              </div>
                           {{/if}}
                        {{/if}}
                     </div>
                  {{/checkIfAny}}                  
               </div>
            {{/checkIfAny}}
         </header>

         <div class="content__inner">
            <div class="content__entry">
               {{{text}}}
            </div>

            {{#checkIfAny @config.post.displayTags @config.post.displayShareButtons }}
					<footer class="content__footer">
                  <div class="content__tag-share">
                     {{#if @config.post.displayTags}}
                        {{#if tags}}
                           <div class="content__tag">
                              <h3 class="content__tag__title">{{ translate 'post.postedIn' }}</h3>
                              <ul class="content__tag__list">
                                 {{#each tags}}
                                    <li>
                                       <a href="{{url}}">{{name}}</a>
                                    </li>
                                 {{/each}}
                              </ul>
                           </div>
                        {{/if}}
                     {{/if}}

                     {{#if @config.post.displayShareButtons}}
                        {{#checkIfAll @plugins.socialSharing @plugins.socialSharing.state}}
                           <div class="content__share">
                              {{{@customSocialSharing}}}
                           </div>
                        {{else}}
                           <div class="content__share">
                              {{> share-buttons}}
                           </div>
                        {{/checkIfAll}}          
                     {{/if}}
                  </div>
               </footer>
            {{/checkIfAny}}    

         </div>
      </article>
   {{/post}}

   {{#if @config.post.displayNewsletter}}
      {{> newsletter}}
   {{/if}}

   {{#if @config.post.displayComments}}
      <div class="comments-area content__inner">
         {{{@commentsCustomCode}}}
      </div>
   {{/if}}

   {{#if @config.post.displayRelatedPosts}}
      {{#if relatedPosts}}
         <div class="content__related">
            <h3 class="content__related__title">
               {{ translate 'post.relatedPosts' }}
            </h3>
            <div class="l-grid {{@config.custom.gridRelatedPostsLayout}}">
               {{#each relatedPosts}}
                  <article class="c-card">
                     <div class="c-card__wrapper">
                        {{#featuredImage}}
                           {{#if url}}
                              <figure class="c-card__image is-img-loading">
                                 <img
                                    src="{{url}}"
                                    {{#if @config.site.responsiveImages}}
                                        {{#checkIf @config.custom.gridRelatedPostsLayout '==' "l-grid--2" }}
                                          {{responsiveImageAttributes 'featuredImage' srcset.cards sizes.cards-col-2}}
                                       {{/checkIf}}
                                       {{#checkIf @config.custom.gridRelatedPostsLayout '==' "l-grid--3" }}
                                          {{responsiveImageAttributes 'featuredImage' srcset.cards sizes.cards-col-3}}
                                       {{/checkIf}}
                                       {{#checkIf @config.custom.gridRelatedPostsLayout '==' "l-grid--4" }}
                                          {{responsiveImageAttributes 'featuredImage' srcset.cards sizes.cards-col-4}}
                                       {{/checkIf}}
                                    {{/if}}
                                    {{ lazyload "lazy" }}
                                    height="{{height}}"
                                    width="{{width}}"
                                    alt="{{alt}}">
                              </figure>
                           {{/if}}
                        {{/featuredImage}}
                        <div class="c-card__content">
                           <header>
                              <h2 class="c-card__title">
                                 <a href="{{url}}" class="invert">
                                    {{title}}
                                 </a>
                              </h2>
                           </header>
                           
                              {{#if @config.custom.cardsTag}}
                              <footer class="c-card__meta">
                                 {{#if mainTag}}
                                    <a href="{{ mainTag.url }}" class="c-card__tag">{{ mainTag.name }}</a>
                                 {{/if}}
                              </footer>
                           {{/if}}
                        </div>
                     </div>
                  </article>
               {{/each}}
            </div>
         </div>
      {{/if}}
   {{/if}}

   {{#if @customHTML.afterPost}}
      <div class="banner banner--after-content">
         {{{@customHTML.afterPost}}}
      </div>
   {{/if}}
   
</main>
{{> footer}}
