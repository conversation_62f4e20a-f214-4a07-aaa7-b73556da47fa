

{{!-- new header --}}
<header class="header marif">
   <div class="header-wrapper">
      <div class="header-logo">
         <a class="logo" href="{{@website.url}}">
            {{#if @website.logo}}
            <img src="{{@website.logo}}" alt="{{@website.name}}"
               width="{{@website.logoSize.width}}"
               height="{{@website.logoSize.height}}">
            {{else}}
            {{@website.name}}
            {{/if}}
         </a>
      </div>
      <div class="menu-and-social">
         <div class="main-menu">
            <div class="mobile-menu-logo">
               <a class="logo" href="{{@website.url}}">
                  {{#if @website.logo}}
                  <img src="{{@website.logo}}" alt="{{@website.name}}"
                     width="{{@website.logoSize.width}}"
                     height="{{@website.logoSize.height}}">
                  {{else}}
                  {{@website.name}}
                  {{/if}}
               </a>
            </div>
            <div class="menu-list">
               {{#if menus.mainMenu}}
               {{> menu menus.mainMenu}}
               {{/if}}
            </div>
         </div>
         <div class="nav-right">
            {{#if @config.custom.socialLinkedin}}
            <ul class="social-area">
               <li>
                  <a href="{{@config.custom.socialLinkedin}}"
                     aria-label="{{ translate 'partials.footer.followLinkedIn' }}">
                     <svg>
                        <use
                           xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#linkedin" />
                     </svg>
                  </a>
               </li>
            </ul>
            {{/if}}
            {{#if @config.custom.searchFeature}}
            <div class="search">
               <div class="search__overlay js-search-overlay">
                  <div class="wrapper search__overlay-inner">
                     {{{@customSearchInput}}}
                  </div>
               </div>
               <button class="search__btn btn--icon js-search-btn"
                  aria-label="{{ translate 'search.search' }}">
                  <svg height="18" width="18" role="presentation"
                     focusable="false">
                     <use
                        xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#search" />
                  </svg>
               </button>
            </div>
            {{/if}}
            <div class="sidebar-button mobile-menu-btn ">
               <span></span>
            </div>
         </div>
      </div>
   </div>
</header>