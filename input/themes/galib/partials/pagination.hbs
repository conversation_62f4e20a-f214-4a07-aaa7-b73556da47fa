{{#if @pagination}}
   <nav class="{{#is "index,tag,author"}}wrapper{{/is}} pagination {{@website.postsOrdering}}">
      {{#checkIf @website.postsOrdering '==' "desc"}}
         {{#if @pagination.nextPage}}
            {{#checkIf @website.postsOrdering '==' "desc"}}
               <div class="pagination__item">
                  <a href="{{@pagination.nextPageUrl}}" class="btn btn--icon">
                     <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-prev"/>
                     </svg> 
                     <span>{{ translate 'partials.pagination.prev' }}</span>
                  </a>
               </div>
            {{else}}
               <div class="pagination__item">
                  <a href="{{@pagination.nextPageUrl}}" class="btn btn--icon">
                     <span>{{ translate 'partials.pagination.next' }}</span> 
                     <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-next"/>
                     </svg> 
                  </a>  
               </div>
            {{/checkIf}}
         {{/if}}
         {{#if @pagination.previousPage}}
            {{#checkIf @website.postsOrdering '==' "desc"}}
               <div class="pagination__item">
                  <a href="{{@pagination.previousPageUrl}}" class="btn btn--icon">
                     <span>{{ translate 'partials.pagination.next' }}</span> 
                     <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-next"/>
                     </svg>
                  </a> 
               </div>
            {{else}}
               <div class="pagination__item">
                  <a href="{{@pagination.previousPageUrl}}" class="btn btn--icon">
                        <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-prev"/>
                     </svg>
                     <span>{{ translate 'partials.pagination.prev' }}</span>
                  </a>
               </div>
            {{/checkIf}}
         {{/if}}
      {{else}}
         {{#if @pagination.previousPage}}
            {{#checkIf @website.postsOrdering '==' "desc"}}
               <div class="pagination__item">
                  <a href="{{@pagination.previousPageUrl}}" class="btn btn--icon">
                     <span>{{ translate 'partials.pagination.next' }}</span> 
                     <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-next"/>
                     </svg>
                  </a> 
               </div>
            {{else}}
               <div class="pagination__item">
                  <a href="{{@pagination.previousPageUrl}}" class="btn btn--icon">
                     <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-prev"/>
                     </svg> 
                     <span>{{ translate 'partials.pagination.prev' }}</span>
                  </a>
               </div>
            {{/checkIf}}
         {{/if}}
         {{#if @pagination.nextPage}}
            {{#checkIf @website.postsOrdering '==' "desc"}}
               <div class="pagination__item">
                  <a href="{{@pagination.nextPageUrl}}" class="btn btn--icon">
                     <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-prev"/>
                     </svg>  
                     <span>{{ translate 'partials.pagination.prev' }}</span>
                  </a>
               </div>
            {{else}}
               <div class="pagination__item">
                  <a href="{{@pagination.nextPageUrl}}" class="btn btn--icon">
                     <span>{{ translate 'partials.pagination.next' }}</span> 
                     <svg width="20" height="20" aria-hidden="true">
                        <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-next"/>
                     </svg>
                  </a> 
               </div>
            {{/checkIf}}
         {{/if}}
      {{/checkIf}}
   </nav>
{{/if}}
