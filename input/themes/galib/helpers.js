module.exports = function (Handlebars, globalData) {
    // Helper to get posts by tag
    Handlebars.registerHelper('getTeamPosts', function(limit) {
        if (!globalData.posts) return [];
        
        const teamPosts = globalData.posts.filter(post => {
            return post.tags && post.tags.some(tag => tag.name === 'team');
        });
        
        return limit ? teamPosts.slice(0, limit) : teamPosts;
    });
};
