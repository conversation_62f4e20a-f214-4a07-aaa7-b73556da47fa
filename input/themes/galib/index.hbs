{{> head}}
{{> navbar}}
<main>
   <div
      class="banner-section {{#checkIf @config.custom.uploadHero '&&' @renderer.isFirstPage}}{{else}}hero--noimage{{/checkIf}}"
      style="background-image: linear-gradient(270deg, rgba(255, 255, 255, 0.31) 0%, rgba(0, 0, 0, 0.31) 100%), url({{@config.custom.uploadHero}});">
      <div class="container-one">
         <div class="row">
            <div class="col-lg-12">
               <div class="banner-title aos-init aos-animate"
                  data-aos="fade-up">
                  <div class="line"></div>
                  <h1>{{{@config.custom.titleHero}}}</h1>
               </div>
            </div>
         </div>
      </div>
      {{#if @config.custom.textHero}}
      <div class="banner-content-warp">
         <div class="container-one">
            <div class="row">
               <div class="col-lg-12">
                  <div class="banner-content aos-init" data-aos="fade-up">
                     <div class="line"></div>
                     {{{@config.custom.textHero}}}
                  </div>
               </div>
            </div>
         </div>
      </div>
      {{/if}}
      <div class="sm-news-insignt-sec">
         <div class="container-one">
            <div class="row">
               <div class="col-lg-12">
                  <div class="news-insight-wrap">
                     <h6 class="title">INSIGHTS &amp; NEWS</h6>
                     <div class="news-card-group">
                        <div class="single-sm-news">
                           <a href="single-new.html" class="news-image">
                              <img src="assets/images/insight-sm-01.png" alt>
                           </a>
                           <div class="news-content">
                              <span>LEGAL ALERT:</span>
                              <h6><a href="single-new.html">Experts lorem ipsum
                                    dolor sit amet consect adipiscing, more...
                                 </a></h6>
                              <div class="date">
                                 14 OCTOBER 2024
                              </div>
                           </div>
                        </div>
                        <div class="single-sm-news">
                           <a href="single-new.html" class="news-image">
                              <img src="assets/images/insight-sm-02.png" alt>
                           </a>
                           <div class="news-content">
                              <span>NEWS:</span>
                              <h6> <a href="single-new.html">Experts lorem ipsum
                                    dolor sit amet consect adipiscing, more...
                                 </a></h6>
                              <div class="date">
                                 14 OCTOBER 2024
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="next-section-btn-area">
      <div class="container-one">
         <div class="row">
            <div class="col-lg-12">
               <a href="#next-section" class="next-sec"><img
                     src="assets/images/arrow-down.svg" alt></a>
            </div>
         </div>
      </div>
   </div>

   <div class="wrapper feed">
  {{#each posts}}
  {{#each tags}}
    {{#checkIf name "==" "team"}}
      
      <article
         class="feed__item {{#checkIf @config.custom.alignFeed '==' "center"}}feed__item--centered{{/checkIf}}">
         
         {{#if @config.custom.feedFeaturedImage}}
         {{#featuredImage}}
         {{#if url}}
         <figure class="feed__image">
            <img
               src="{{url}}"
               {{#if @config.site.responsiveImages}}
                 {{responsiveImageAttributes 'featuredImage' srcset.feed sizes.feed}}
               {{/if}}
               {{ lazyload "lazy" }}
               height="{{height}}"
               width="{{width}}"
               alt="{{alt}}">
         </figure>
         {{/if}}
         {{/featuredImage}}
         {{/if}}

         <div class="feed__content">
            <header>
               {{#checkIfAny @config.custom.feedAvatar @config.custom.feedAuthor @config.custom.feedDate}}
               <div class="feed__meta">
                  {{#author}}
                  {{#if @config.custom.feedAvatar}}
                  {{#if avatar}}
                  {{#if @config.custom.feedAuthor}}
                  <img
                     src="{{avatarImage.url}}"
                     {{ lazyload "lazy" }}
                     height="{{avatarImage.height}}"
                     width="{{avatarImage.width}}"
                     class="feed__author-thumb"
                     alt>
                  {{else}}
                  <a href="{{url}}" class="feed__author-link">
                     <img
                        src="{{avatarImage.url}}"
                        {{ lazyload "lazy" }}
                        height="{{avatarImage.height}}"
                        width="{{avatarImage.width}}"
                        class="feed__author-thumb"
                        alt="{{avatarImage.alt}}">
                  </a>
                  {{/if}}
                  {{/if}}
                  {{/if}}
                  {{#if @config.custom.feedAuthor}}
                  <a href="{{url}}" class="feed__author">{{name}}</a>
                  {{/if}}
                  {{/author}}

                  {{#if @config.custom.feedDate}}
                  {{#checkIf @config.custom.feedDateType '==' "published" }}
                  <time datetime="{{date createdAt 'YYYY-MM-DDTHH:mm'}}"
                     class="feed__date">
                     {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                     {{date createdAt @config.custom.formatDate}}
                     {{else}}
                     {{date createdAt @config.custom.formatDateCustom}}
                     {{/checkIf}}
                  </time>
                  {{/checkIf}}
                  {{#checkIf @config.custom.feedDateType '==' "modified" }}
                  <time datetime="{{date modifiedAt 'YYYY-MM-DDTHH:mm'}}"
                     class="feed__date">
                     {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                     {{date modifiedAt @config.custom.formatDate}}
                     {{else}}
                     {{date modifiedAt @config.custom.formatDateCustom}}
                     {{/checkIf}}
                  </time>
                  {{/checkIf}}
                  {{/if}}
               </div>
               {{/checkIfAny}}
               <h2 class="feed__title">
                  <a href="{{url}}">
                     {{title}}
                  </a>
               </h2>
            </header>
            {{#if hasCustomExcerpt}}
            {{{ excerpt }}}
            {{else}}
            <p>{{{ excerpt }}}</p>
            {{/if}}
            {{#if @config.custom.feedtReadMore}}
            <a href="{{url}}" class="readmore feed__readmore">
               {{ translate 'post.readMore' }}</a>
            {{/if}}
         </div>
      </article>

    {{/checkIf}}
  {{/each}}
{{/each}}


      {{> pagination}}
   </div>

   <div class="team-section mb-35"
      style="background-image: url(assets/images/team-bg.png);">
      <div class="container-one">
         <div class="row">
            <div class="col-lg-12">
               <div class="section-title white aos-init aos-animate"
                  data-aos="fade-up">
                  <h6>OUR TEAM</h6>
                  <div class="line mt-20"></div>
               </div>
               <div class="row g-4 mb-40">
                  <div class="col-md-4 col-sm-6 aos-init aos-animate"
                     data-aos="fade-up">
                     <div class="people-card">
                        <a href="single-people.html" class="people-img"><img
                              src="assets/images/tram-01.png" alt></a>
                        <div class="people-content">
                           <h6><a href="single-people.html">Name Name</a></h6>
                           <span>Partner</span>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-4 col-sm-6 aos-init aos-animate"
                     data-aos="fade-up">
                     <div class="people-card">
                        <a href="single-people.html" class="people-img"><img
                              src="assets/images/tram-02.png" alt></a>
                        <div class="people-content">
                           <h6><a href="single-people.html">Name Name</a></h6>
                           <span>Partner</span>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-4 col-sm-6 aos-init aos-animate"
                     data-aos="fade-up">
                     <div class="people-card">
                        <a href="single-people.html" class="people-img"><img
                              src="assets/images/tram-03.png" alt></a>
                        <div class="people-content">
                           <h6><a href="single-people.html">Name Name</a></h6>
                           <span>Partner</span>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="button-area mb-25 aos-init" data-aos="fade-up">
                  <a class="primary-btn" href="people.html">OUR TEAM</a>
               </div>
               <div class="line aos-init" data-aos="fade-up"></div>
            </div>
         </div>
      </div>
   </div>
</main>
{{> footer}}
