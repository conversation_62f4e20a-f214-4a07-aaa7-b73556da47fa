{{> head}}
{{> navbar}}
<main class="post">
   {{#post}}
      <article class="content">
         <div class="hero {{#checkIfNone featuredImage.url}}hero--noimage{{/checkIfNone}}">
            <header class="hero__content {{#checkIf @config.custom.alignHero '==' "center" }}hero__content--centered{{/checkIf}}">
               <div class="wrapper">
                  <h1>{{title}}</h1>
                   {{#checkIfAny @config.post.displayAuthor @config.post.displayDate}}
                     <div class="feed__meta content__meta {{#checkIf @config.custom.alignHero '==' "center" }}content__meta--centered{{/checkIf}} ">
                        {{#if @config.post.displayAuthor}}
                           {{#author}}
                              {{#if avatar}}
                                 <img 
                                    src="{{avatarImage.url}}" 
                                    {{ lazyload "eager" }}
                                    height="{{avatarImage.height}}"
                                    width="{{avatarImage.width}}"
                                    class="feed__author-thumb" 
                                    alt="">
                              {{/if}}
                              <a href="{{url}}" class="feed__author">{{name}}</a>
                           {{/author}}
                        {{/if}}

                        {{#if @config.post.displayDate}}
                           <time datetime="{{date createdAt 'YYYY-MM-DDTHH:mm'}}" class="feed__date">
                              {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                 {{date createdAt @config.custom.formatDate}}
                              {{else}}
                                 {{date createdAt @config.custom.formatDateCustom}}
                              {{/checkIf}}
                           </time>
                        {{/if}}
                     </div>
                  {{/checkIfAny}}
                </div>
            </header>

            {{#featuredImage}}
               {{#if url}}
                  <figure class="hero__image">  
                     <div class="hero__image-wrapper">                    
                        <img
                           src="{{url}}"
                           {{#if @config.site.responsiveImages}}                           
                              {{responsiveImageAttributes 'featuredImage' srcset.hero sizes.hero}}
                           {{/if}}
                           {{ lazyload "eager" }}
                           height="{{height}}"
                           width="{{width}}"
                           alt="{{alt}}">
                        </div>
                      
                     {{#checkIfAny caption credits}}
                        <figcaption>
                           {{caption}}
                           {{credits}}
                        </figcaption>                      
                     {{/checkIfAny}}                      
                  </figure>
               {{/if}}
            {{/featuredImage}}     
         </div>

         <div class="entry-wrapper content__entry">           
            {{{text}}}            
         </div>

         {{#checkIfAny @config.post.displayLastUpdatedDate @config.post.displayTags @config.post.displayShareButtons @config.post.displayAuthorBio @config.post.displayPostNavigation}}
            <footer class="content__footer">
               <div class="entry-wrapper">
                  {{#if @config.post.displayLastUpdatedDate}}
                     {{#if modifiedAt}}
                        <p class="content__updated">
                           {{ translate 'post.lastUpdatedDate' }}
                           {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                              {{date modifiedAt @config.custom.formatDate}}
                           {{else}}
                              {{date modifiedAt @config.custom.formatDateCustom}}
                           {{/checkIf}}
                        </p>
                     {{/if}}
                  {{/if}}

                  {{#checkIfAny @config.post.displayTags @config.post.displayShareButtons}}
                     <div class="content__actions">
                        {{#if @config.post.displayTags}}
                           {{#if tags}}
                              <ul class="content__tag">
                                 {{#each tags}}
                                    <li>
                                       <a href="{{url}}">{{name}}</a>
                                    </li>
                                 {{/each}}
                              </ul>
                           {{/if}}
                        {{/if}}

                        {{#if @config.post.displayShareButtons}}
                           <div class="content__share">
                              <button class="btn--icon content__share-button js-content__share-button">
                                 <svg width="20" height="20" aria-hidden="true">
                                       <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#share"></use>
                                 </svg> 
                                 <span>{{ translate 'post.shareIt' }}</span>
                              </button>
                              {{#checkIfAll @plugins.socialSharing @plugins.socialSharing.state}}
                                 <div class="content__share-popup js-content__share-popup">
                                    {{{@customSocialSharing}}}
                                 </div>
                              {{else}}
                                 <div class="content__share-popup js-content__share-popup">
                                    {{> share-buttons}}
                                 </div>
                              {{/checkIfAll}}   
                           </div>       
                        {{/if}}
                     </div>
                  {{/checkIfAny}}

                  {{#if @config.post.displayAuthorBio}}
                     <div class="content__bio bio">
                        {{#author}}
                           {{#if avatar}}                          
                              <img 
                                 src="{{avatarImage.url}}" 
                                 {{ lazyload "lazy" }}
                                 height="{{avatarImage.height}}"
                                 width="{{avatarImage.width}}" 
                                 class="bio__avatar" 
                                 alt="">                          
                           {{/if}}
                           <div>
                              <h3 class="h4 bio__name">
                                 <a href="{{url}}" rel="author">{{name}}</a>
                              </h3>
                              {{#if description}}
                                 <div class="bio__desc">
                                    {{{description}}}
                                 </div>
                              {{/if}}
                           </div>
                        {{/author}}
                     </div>
                  {{/if}}
               </div>

               {{#if @config.post.displayPostNavigation}}
                  {{#checkIfAny ../previousPost ../nextPost}}
                     <nav class="content__nav">
                        <div class="wrapper">
                           <div class="content__nav-inner">
                              {{#../previousPost}}
                                 <div class="content__nav-prev">
                                    <a href="{{url}}" class="content__nav-link" rel="prev">
                                       {{#featuredImage}}
                                          {{#if url}} 
                                             <figure class="content__nav-image">
                                                <img 
                                                   {{#if @config.site.responsiveImages}}
                                                      src="{{urlXs}}"
                                                   {{else}}
                                                      src="{{url}}"
                                                   {{/if}}
                                                   class="lazyload"
                                                   {{ lazyload "lazy" }} 
                                                   alt="{{alt}}" 
                                                   height="{{height}}" 
                                                   width="{{height}}">
                                              </figure>                    
                                          {{/if}}
                                       {{/featuredImage}}
                                       <div>
                                          <span>{{ translate 'post.previousPost' }}</span>
                                          {{title}}
                                       </div>
                                    </a>
                                 </div>
                              {{/../previousPost}}
                              {{#../nextPost}}
                                 <div class="content__nav-next">
                                    <a href="{{url}}" class="content__nav-link" rel="next">
                                       <div>
                                          <span>{{ translate 'post.nextPost' }}</span>
                                          {{title}}
                                       </div>
                                       {{#featuredImage}}
                                          {{#if url}} 
                                             <figure class="content__nav-image">
                                                <img 
                                                   {{#if @config.site.responsiveImages}}
                                                      src="{{urlXs}}"
                                                   {{else}}
                                                      src="{{url}}"
                                                   {{/if}}
                                                   class="lazyload"
                                                   {{ lazyload "lazy" }} 
                                                   alt="{{alt}}" 
                                                   height="{{height}}" 
                                                   width="{{height}}">
                                              </figure>                    
                                          {{/if}}
                                       {{/featuredImage}}
                                    </a>
                                 </div>
                              {{/../nextPost}}
                           </div>
                        </div>
                     </nav>
                  {{/checkIfAny}}
               {{/if}}
            </footer>
         {{/checkIfAny}}
      </article>

       {{#if @config.post.displayComments}}
         <div class="content__comments">
            <div class="entry-wrapper">
               {{{@commentsCustomCode}}}
            </div>
         </div>
      {{/if}}

      {{#if @config.post.displayRelatedPosts}}
         {{#if ../relatedPosts}}
            <div class="content__related related">
               <div class="wrapper">
                  <h2 class="h4 related__title">
                     {{ translate 'post.relatedPosts' }}
                  </h2>
                  {{#each ../relatedPosts}}
                     <article class="feed__item {{#checkIf @config.custom.alignFeed '==' "center" }}feed__item--centered{{/checkIf}}">
                        {{#if @config.custom.feedFeaturedImage}}
                           {{#featuredImage}}
                              {{#if url}}
                                 <figure class="feed__image related__image">
                                    <img
                                       src="{{url}}"
                                       {{#if @config.site.responsiveImages}}
                                          {{responsiveImageAttributes 'featuredImage' srcset.feed sizes.feed}}
                                       {{/if}}
                                       {{ lazyload "lazy" }}
                                       height="{{height}}"
                                       width="{{width}}"
                                       alt="{{alt}}">
                                 </figure>
                              {{/if}}
                           {{/featuredImage}}
                        {{/if}}
                        <div class="feed__content">
                           <header>
                              {{#checkIfAny @config.custom.feedAvatar @config.custom.feedAuthor @config.custom.feedDate}}
                                 <div class="feed__meta">
                                    {{#author}}
                                       {{#if @config.custom.feedAvatar}}
                                          {{#if avatar}}
                                             {{#if @config.custom.feedAuthor}}
                                                <img
                                                   src="{{avatarImage.url}}" 
                                                   {{ lazyload "lazy" }}
                                                   height="{{avatarImage.height}}"
                                                   width="{{avatarImage.width}}"      
                                                   class="feed__author-thumb"
                                                   alt="">
                                             {{else}}
                                                <a href="{{url}}" class="feed__author-link">
                                                   <img
                                                      src="{{avatarImage.url}}" 
                                                      {{ lazyload "lazy" }}
                                                      height="{{avatarImage.height}}"
                                                      width="{{avatarImage.width}}"      
                                                      class="feed__author-thumb"
                                                      alt="{{avatarImage.alt}}">
                                                </a>
                                             {{/if}}
                                          {{/if}}
                                       {{/if}}
                                       {{#if @config.custom.feedAuthor}}
                                          <a href="{{url}}" class="feed__author">{{name}}</a>
                                       {{/if}}
                                    {{/author}}
                                    {{#if @config.custom.feedDate}}
                                       {{#checkIf @config.custom.feedDateType '==' "published" }}
                                          <time datetime="{{date createdAt 'YYYY-MM-DDTHH:mm'}}" class="feed__date">
                                             {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                                {{date createdAt @config.custom.formatDate}}
                                             {{else}}
                                                {{date createdAt @config.custom.formatDateCustom}}
                                             {{/checkIf}}
                                          </time>
                                       {{/checkIf}}
                                       {{#checkIf @config.custom.feedDateType '==' "modified" }}
                                          <time datetime="{{date modifiedAt 'YYYY-MM-DDTHH:mm'}}" class="feed__date">
                                             {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                                {{date modifiedAt @config.custom.formatDate}}
                                             {{else}}
                                                {{date modifiedAt @config.custom.formatDateCustom}}
                                             {{/checkIf}}
                                          </time>
                                       {{/checkIf}}
                                    {{/if}}
                                 </div>
                              {{/checkIfAny}}
                              <h3 class="feed__title">
                                 <a href="{{url}}">
                                    {{title}}
                                 </a>
                              </h3>
                           </header>               
                           {{#if hasCustomExcerpt}}
                              {{{ excerpt }}}
                           {{else}}
                              <p>{{{ excerpt }}}</p>
                           {{/if}}
                           {{#if @config.custom.feedtReadMore}}
                              <a href="{{url}}" class="readmore feed__readmore">
                                 {{ translate 'post.readMore' }}</a>
                           {{/if}}
                        </div>
                     </article>
                  {{/each}}
               </div>
            </div>
         {{/if}}
      {{/if}}

      {{#if @customHTML.afterPost}}
         <div class="banner banner--after-content">
            <div class="wrapper">
               {{{@customHTML.afterPost}}}
            </div>
         </div>
      {{/if}}

   {{/post}}
</main>
{{> footer}}
