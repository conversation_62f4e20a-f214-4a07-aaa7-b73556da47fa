{{> head}}
{{> navbar}}
<main class="post post--team">
   {{#post}}
      <article class="content">
         <div class="hero {{#checkIfNone featuredImage.url}}hero--noimage{{/checkIfNone}}" {{#if featuredImage.url}}
      style="background-image: url('{{featuredImage.url}}'); background-size: cover; background-position: center;"
   {{/if}}>
            <header class="hero__content hero__content--centered">
               <div class="wrapper">
                  <h1>{{title}}</h1>
                  {{#if customFields.position}}
                     <div class="team-position">
                        <h2 class="team-position__title">{{customFields.position}}</h2>
                     </div>
                  {{/if}}
                  
                  {{#checkIfAny @config.post.displayAuthor @config.post.displayDate}}
                     <div class="feed__meta content__meta content__meta--centered">
                        {{#if @config.post.displayAuthor}}
                           {{#author}}
                              {{#if avatar}}
                                 <img 
                                    src="{{avatarImage.url}}" 
                                    {{ lazyload "eager" }}
                                    height="{{avatarImage.height}}"
                                    width="{{avatarImage.width}}"
                                    class="feed__author-thumb" 
                                    alt="">
                              {{/if}}
                              <a href="{{url}}" class="feed__author">{{name}}</a>
                           {{/author}}
                        {{/if}}

                        {{#if @config.post.displayDate}}
                           <time datetime="{{date createdAt 'YYYY-MM-DDTHH:mm'}}" class="feed__date">
                              {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                 {{date createdAt @config.custom.formatDate}}
                              {{else}}
                                 {{date createdAt @config.custom.formatDateCustom}}
                              {{/checkIf}}
                           </time>
                        {{/if}}
                     </div>
                  {{/checkIfAny}}
                </div>
            </header>

            {{#featuredImage}}
               {{#if url}}
                  <figure class="hero__image team-photo">  
                     <div class="hero__image-wrapper">                    
                        <img
                           src="{{url}}"
                           {{#if @config.site.responsiveImages}}                           
                              {{responsiveImageAttributes 'featuredImage' srcset.hero sizes.hero}}
                           {{/if}}
                           {{ lazyload "eager" }}
                           height="{{height}}"
                           width="{{width}}"
                           alt="{{alt}}">
                     </div>
                     {{#checkIfAny caption credits}}
                        <figcaption class="hero__caption">
                           {{{caption}}}
                           {{{credits}}}
                        </figcaption>
                     {{/checkIfAny}}
                  </figure>
               {{/if}}
            {{/featuredImage}}
         </div>

         <div class="entry-wrapper">
            <div class="entry">
               {{#if customFields.bio}}
                  <div class="team-bio">
                     <h3>About {{title}}</h3>
                     <div class="team-bio__content">
                        {{{customFields.bio}}}
                     </div>
                  </div>
               {{/if}}

               {{#if customFields.skills}}
                  <div class="team-skills">
                     <h3>Skills & Expertise</h3>
                     <div class="team-skills__content">
                        {{customFields.skills}}
                     </div>
                  </div>
               {{/if}}

               <div class="team-content">
                  {{{text}}}
               </div>

               {{#if customFields.social_links}}
                  <div class="team-social">
                     <h3>Connect with {{title}}</h3>
                     <div class="team-social__links">
                        {{{customFields.social_links}}}
                     </div>
                  </div>
               {{/if}}

               {{#if @config.post.displayTags}}
                  {{#if tags}}
                     <footer class="content__footer">
                        <div class="content__tag">
                           {{#each tags}}
                              <a href="{{url}}" class="content__tag-btn">{{name}}</a>
                           {{/each}}
                        </div>
                     </footer>
                  {{/if}}
               {{/if}}
            </div>
         </div>

         {{#if @config.post.displayShareButtons}}
            {{> share-buttons}}
         {{/if}}

         {{#if @config.post.displayAuthorBio}}
            {{#author}}
               {{#if description}}
                  <div class="content__author">
                     <div class="content__author-wrapper">
                        {{#if avatar}}
                           <div class="content__author-image">
                              <img 
                                 src="{{avatarImage.url}}" 
                                 {{ lazyload "lazy" }}
                                 height="{{avatarImage.height}}"
                                 width="{{avatarImage.width}}"
                                 alt="{{avatarImage.alt}}">
                           </div>
                        {{/if}}
                        <div class="content__author-info">
                           <h3 class="content__author-name">
                              <a href="{{url}}" rel="author">{{name}}</a>
                           </h3>
                           <div class="content__author-bio">
                              {{{description}}}
                           </div>
                        </div>
                     </div>
                  </div>
               {{/if}}
            {{/author}}
         {{/if}}

         {{#if @config.post.displayPostNavigation}}
            <nav class="content__nav">
               {{#if previousPost}}
                  <div class="content__nav-prev">
                     <a href="{{previousPost.url}}" class="content__nav-link content__nav-link--prev">
                        <figure>
                           {{#if previousPost.featuredImage.url}}
                              <img 
                                 src="{{previousPost.featuredImage.url}}" 
                                 {{ lazyload "lazy" }}
                                 height="{{previousPost.featuredImage.height}}"
                                 width="{{previousPost.featuredImage.width}}"
                                 alt="{{previousPost.featuredImage.alt}}">
                           {{/if}}
                        </figure>
                        <div>
                           <span>{{ translate 'post.previousPost' }}</span>
                           {{previousPost.title}}
                        </div>
                     </a>
                  </div>
               {{/if}}
               {{#if nextPost}}
                  <div class="content__nav-next">
                     <a href="{{nextPost.url}}" class="content__nav-link content__nav-link--next">
                        <figure>
                           {{#if nextPost.featuredImage.url}}
                              <img 
                                 src="{{nextPost.featuredImage.url}}" 
                                 {{ lazyload "lazy" }}
                                 height="{{nextPost.featuredImage.height}}"
                                 width="{{nextPost.featuredImage.width}}"
                                 alt="{{nextPost.featuredImage.alt}}">
                           {{/if}}
                        </figure>
                        <div>
                           <span>{{ translate 'post.nextPost' }}</span>
                           {{nextPost.title}}
                        </div>
                     </a>
                  </div>
               {{/if}}
            </nav>
         {{/if}}
      </article>
   {{/post}}

   {{#if @config.post.displayRelatedPosts}}
      {{#if relatedPosts}}
         <div class="related">
            <div class="wrapper">
               <h2 class="related__title">{{ translate 'post.relatedPosts' }}</h2>
               <div class="related__list">
                  {{#each relatedPosts}}
                     <article class="related__item">
                        <div class="related__item-wrapper">
                           {{#featuredImage}}
                              {{#if url}}
                                 <figure class="related__item-image">
                                    <img
                                       src="{{url}}"
                                       {{#if @config.site.responsiveImages}}
                                          {{responsiveImageAttributes 'featuredImage' srcset.related sizes.related}}
                                       {{/if}}
                                       {{ lazyload "lazy" }}
                                       height="{{height}}"
                                       width="{{width}}"
                                       alt="{{alt}}">
                                 </figure>
                              {{/if}}
                           {{/featuredImage}}
                           <div class="related__item-content">
                              <h3 class="related__item-title">
                                 <a href="{{url}}">{{title}}</a>
                              </h3>
                              {{#if excerpt}}
                                 <div class="related__item-excerpt">
                                    {{{excerpt}}}
                                 </div>
                              {{/if}}
                           </div>
                        </div>
                     </article>
                  {{/each}}
               </div>
            </div>
         </div>
      {{/if}}
   {{/if}}
</main>
{{> footer}}
