{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "author": "Custom Theme based on Simple by TidyCustoms", "customScripts": ["assets/js/custom-wysiwyg.js"], "menus": {"mainMenu": {"desc": "", "name": "Main menu", "maxLevels": -1}, "footerMenu": {"desc": "", "name": "Footer menu", "maxLevels": 1}}, "renderer": {"relatedPostsNumber": 3, "renderRelatedPosts": true, "renderSimilarPosts": false, "renderPrevNextPosts": true, "createContentStructure": true, "createTagPages": true, "createAuthorPages": true, "createTagsList": true, "createSearchPage": false, "create404page": true, "customHTML": {"afterPost": "After every post", "afterPage": "After every page"}}, "supportedFeatures": {"blockEditor": true, "tagsList": true, "tagPages": true, "tagImages": true, "authorPages": true, "authorImages": true, "searchPage": true, "errorPage": true, "customSharing": true, "customSearch": true, "customComments": true, "embedConsents": true, "pages": true}, "pageTemplates": {"empty": "Empty container"}, "postTemplates": {"team": "🧑‍💼 Team Member Profile", "blog": "📝 Enhanced Blog Post"}, "config": [{"name": "postsPerPage", "label": "Posts per page", "value": 5, "type": "number"}, {"name": "tagsPostsPerPage", "label": "Tags posts per page", "value": 5, "type": "number"}, {"name": "authorsPostsPerPage", "label": "Authors posts per page", "value": 5, "type": "number"}, {"name": "excerptLength", "label": "Excerpt length", "value": 45, "type": "number"}, {"name": "logo", "label": "Website logo", "value": "", "type": "upload", "upload": true}], "customConfig": [{"name": "pageMargin", "label": "Page margin", "group": "Layout", "value": "6vw", "type": "text"}, {"name": "pageWidth", "label": "Page width", "group": "Layout", "note": "Defines the overall width of the page. This should always be greater than or equal to the Entry width to ensure proper layout. Set to 100% for full-screen width.", "value": "66rem", "type": "text"}, {"name": "entryWidth", "label": "Entry width", "group": "Layout", "note": "Defines the width of content for posts and pages. Ensure that this value is smaller than or equal to the Page width for proper content display. Set to 100% for full-screen width.", "value": "42rem", "type": "text"}, {"name": "borderRadius", "label": "Corner Roundness", "group": "Layout", "note": "Adjust the roundness of corners for elements like images, buttons or dropdown menus. Increasing this value (in pixels) will make the corners more rounded.", "value": "3", "type": "range", "min": 0, "max": 100, "step": 1}, {"name": "baseline", "label": "Baseline", "group": "Layout", "note": "Defines a core vertical rhythm unit for consistent spacing across the website, used as a multiplier for margins and paddings. Increasing this value enhances spacing, creating an airier layout, while decreasing it compacts content, for tighter visual appeal.", "value": "0.28333rem", "type": "text"}, {"name": "separator", "type": "separator", "label": "Hero section", "group": "Layout", "size": "big"}, {"name": "alignHero", "label": "Content alignment", "group": "Layout", "value": "left", "type": "radio", "options": [{"label": "Left", "value": "left"}, {"label": "Center", "value": "center"}]}, {"name": "titleHero", "label": "Title", "group": "Layout", "value": "", "type": "text"}, {"name": "textHero", "label": "Text", "group": "Layout", "value": "", "type": "wysiwyg"}, {"name": "heightHero", "label": "Image height", "group": "Layout", "value": "50vh", "type": "text"}, {"name": "uploadHero", "label": "Image", "group": "Layout", "value": "", "type": "upload", "upload": true}, {"name": "uploadHeroAlt", "label": "Image Alt text", "group": "Layout", "placeholder": "Add an alternative text to the hero image", "value": "", "type": "text"}, {"name": "uploadHeroCaption", "label": "Image Caption", "group": "Layout", "placeholder": "Add text caption to the hero image", "value": "", "type": "text"}, {"name": "separator", "type": "separator", "label": "Post page", "group": "Layout", "size": "big"}, {"name": "relatedPostsNumber", "label": "Related post", "group": "Layout", "note": "Specify number of related posts to show. Zero '0' means no posts.", "value": "3", "type": "number"}, {"name": "separator", "type": "separator", "label": "Home Page", "group": "Layout", "size": "big"}, {"name": "teambg", "label": "Team Section Background", "group": "Layout", "value": "", "type": "upload", "upload": true}, {"name": "separator", "type": "separator", "label": "", "group": "Post list", "note": "This section lets you choose what shows up in your feed on the homepage, tags, and author pages. You can pick and choose which details to show, like author avatars, post dates, and other things.", "size": "no-line"}, {"name": "alignFeed", "label": "Content alignment", "group": "Post list", "value": "center", "type": "radio", "options": [{"label": "Left", "value": "left"}, {"label": "Center", "value": "center"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Post list", "size": "small thin "}, {"name": "feedFeaturedImage", "group": "Post list", "label": "Show Featured Image", "value": false, "type": "checkbox"}, {"name": "feedFeaturedImageSize", "label": "Image size", "group": "Post list", "note": "Adjust the size of the featured image in REM value.", "value": "8", "type": "range", "min": 1, "max": 50, "step": 1, "dependencies": [{"field": "feedFeaturedImage", "value": true}]}, {"name": "feedAvatar", "group": "Post list", "label": "Show author avatar", "value": true, "type": "checkbox"}, {"name": "feedAuthor", "group": "Post list", "label": "Show author name", "value": true, "type": "checkbox"}, {"name": "feedDate", "group": "Post list", "label": "Show date", "value": true, "type": "checkbox"}, {"name": "feedDateType", "group": "Post list", "label": "", "value": "published", "type": "radio", "options": [{"label": "Published date", "value": "published"}, {"label": "Modified date", "value": "modified"}], "dependencies": [{"field": "feedDate", "value": true}]}, {"name": "feedtReadMore", "group": "Post list", "label": "Show 'Read more' button", "value": true, "type": "checkbox"}, {"name": "navbarHeight", "group": "<PERSON><PERSON><PERSON>", "label": "Navbar height", "value": "6rem", "type": "text"}, {"name": "separator", "type": "separator", "label": "Dropdown menu", "group": "<PERSON><PERSON><PERSON>", "size": "big"}, {"name": "submenu", "label": "<PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON>", "value": "auto", "type": "radio", "options": [{"label": "Auto", "value": "auto"}, {"label": "Custom", "value": "custom"}]}, {"name": "submenuWidth", "group": "<PERSON><PERSON><PERSON>", "note": "The submenu width in PX unit", "label": "", "value": "240", "type": "range", "min": 0, "max": 1000, "step": 10, "dependencies": [{"field": "submenu", "value": "custom"}]}, {"name": "separator", "type": "separator", "label": "Mobile menu", "group": "<PERSON><PERSON><PERSON>", "size": "big"}, {"name": "mobilemenu", "label": "Type", "group": "<PERSON><PERSON><PERSON>", "value": "sidebar", "type": "radio", "options": [{"label": "Sidebar", "value": "sidebar"}, {"label": "Overlay", "value": "overlay"}]}, {"name": "mobilemenuExpandableSubmenus", "label": "Expandable submenus", "group": "<PERSON><PERSON><PERSON>", "value": true, "type": "checkbox"}, {"name": "colorScheme", "label": "Select color scheme", "group": "Colors", "note": "<br>The auto color scheme feature automatically adjusts the color scheme of the interface to match the user's operating system settings. If the operating system does not support this feature, the light version of the color scheme is used instead", "value": "auto", "type": "radio", "options": [{"label": "Light", "value": "light"}, {"label": "Dark", "value": "dark"}, {"label": "Auto", "value": "auto"}]}, {"name": "primaryColor", "label": "Primary color (Light mode)", "group": "Colors", "value": "#D73A42", "type": "colorpicker"}, {"name": "primaryDarkColor", "label": "Primary color (Dark mode)", "group": "Colors", "value": "#FFC074", "type": "colorpicker"}, {"name": "separator", "type": "separator", "label": "Main font settings", "group": "Fonts", "note": "To explore an extensive list of available fonts, along with detailed information about their typefaces, complete range of weights, and other specifications, please visit our <a href='https://getpublii.com/docs/fonts.html' target='_blank'>documentation</a>.", "size": "small"}, {"name": "fontBody", "label": "Body font", "group": "Fonts", "value": "system-ui", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui", "group": "System"}, {"label": "<PERSON><PERSON>", "value": "aleo", "group": "<PERSON><PERSON>"}, {"label": "Andada Pro", "value": "andadapro", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "besley", "group": "<PERSON><PERSON>"}, {"label": "Bitter", "value": "bitter", "group": "<PERSON><PERSON>"}, {"label": "Brygada 1918", "value": "brygada1918", "group": "<PERSON><PERSON>"}, {"label": "Domine", "value": "domine", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "faustina", "group": "<PERSON><PERSON>"}, {"label": "<PERSON>", "value": "frankruhllibre", "group": "<PERSON><PERSON>"}, {"label": "Imbue", "value": "imbue", "group": "<PERSON><PERSON>"}, {"label": "K<PERSON><PERSON>", "value": "kreon", "group": "<PERSON><PERSON>"}, {"label": "Labrada", "value": "labrada", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "lora", "group": "<PERSON><PERSON>"}, {"label": "Manuale", "value": "manuale", "group": "<PERSON><PERSON>"}, {"label": "Petrona", "value": "petrona", "group": "<PERSON><PERSON>"}, {"label": "Playfair Display", "value": "playfairdisplay", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON> Slab", "value": "robotoslab", "group": "<PERSON><PERSON>"}, {"label": "Rokkitt", "value": "rokkitt", "group": "<PERSON><PERSON>"}, {"label": "Yrsa", "value": "yrsa", "group": "<PERSON><PERSON>"}, {"label": "Advent Pro", "value": "adventpro", "group": "Sans Serif"}, {"label": "Antonio", "value": "antonio", "group": "Sans Serif"}, {"label": "Archivo Narrow", "value": "archivonarrow", "group": "Sans Serif"}, {"label": "Asap", "value": "asap", "group": "Sans Serif"}, {"label": "Assistant", "value": "assistant", "group": "Sans Serif"}, {"label": "Cabin", "value": "cabin", "group": "Sans Serif"}, {"label": "Cairo", "value": "cairo", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "dosis", "group": "Sans Serif"}, {"label": "Exo", "value": "exo", "group": "Sans Serif"}, {"label": "Figtree", "value": "figtree", "group": "Sans Serif"}, {"label": "Glory", "value": "glory", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "he<PERSON>o", "group": "Sans Serif"}, {"label": "Instrument Sans", "value": "instrumentsans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "jura", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "karla", "group": "Sans Serif"}, {"label": "Lexend", "value": "lexend", "group": "Sans Serif"}, {"label": "Libre Franklin", "value": "librefranklin", "group": "Sans Serif"}, {"label": "Manrope", "value": "manrope", "group": "Sans Serif"}, {"label": "Maven <PERSON>", "value": "mavenpro", "group": "Sans Serif"}, {"label": "Merriweather Sans", "value": "merriweather<PERSON>s", "group": "Sans Serif"}, {"label": "Montserrat", "value": "montserrat", "group": "Sans Serif"}, {"label": "Nunito", "value": "nunito", "group": "Sans Serif"}, {"label": "Orbitron", "value": "orbitron", "group": "Sans Serif"}, {"label": "<PERSON>", "value": "oswald", "group": "Sans Serif"}, {"label": "Plus Jakarta Sans", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "Sans Serif"}, {"label": "Public Sans", "value": "publicsans", "group": "Sans Serif"}, {"label": "Quicksand", "value": "quicksand", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "raleway", "group": "Sans Serif"}, {"label": "Red Hat Display", "value": "redhatdisplay", "group": "Sans Serif"}, {"label": "Roboto Flex", "value": "robotoflex", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "rubik", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "ruda", "group": "Sans Serif"}, {"label": "Smooch Sans", "value": "smoochsans", "group": "Sans Serif"}, {"label": "Spartan", "value": "spartan", "group": "Sans Serif"}, {"label": "Urbanist", "value": "urbanist", "group": "Sans Serif"}, {"label": "Work Sans", "value": "worksans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "yanonekaffeesatz", "group": "Sans Serif"}, {"label": "Big Shoulders Display", "value": "bigshouldersdisplay", "group": "Cursive"}, {"label": "Comfortaa", "value": "comfortaa", "group": "Cursive"}, {"label": "<PERSON>ript", "value": "dancingscript", "group": "Cursive"}, {"label": "Gluten", "value": "gluten", "group": "Cursive"}, {"label": "Lemonada", "value": "lemonada", "group": "Cursive"}, {"label": "JetBrains Mono", "value": "jetbrainsmono", "group": "Monospace"}, {"label": "Red Hat Mono", "value": "redhatmon<PERSON>", "group": "Monospace"}, {"label": "Source Code Pro", "value": "sourcecodepro", "group": "Monospace"}]}, {"name": "disableFontBodyItalic", "label": "Disable italic style", "group": "Fonts", "note": "This option allows you to disable the loading of the dedicated italic version for the body font. The italic font is automatically loaded when selecting a body font that supports it, like Lora, for optimal appearance. However, for performance reasons, you can choose to prevent loading the dedicated italic version by enabling this option.", "type": "checkbox", "value": false, "dependencies": [{"field": "fontBody", "value": "adventpro,aleo,andadapro,archivonarrow,asap,besley,bitter,brygada1918,cabin,exo,faustina,figtree,glory,instrumentsans,jetbrainsmono,karla,labrada,librefranklin,lora,manuale,merriweathersans,montserrat,nunito,petrona,playfairdisplay,plusjakartasans,publicsans,raleway,redhatdisplay,redhatmono,rokkitt,rubik,sourcecodepro,urbanist,worksans,yrsa"}]}, {"name": "fontHeadings", "label": "Headings font (H1-H6)", "group": "Fonts", "value": "system-ui", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui", "group": "System"}, {"label": "<PERSON><PERSON>", "value": "aleo", "group": "<PERSON><PERSON>"}, {"label": "Andada Pro", "value": "andadapro", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "besley", "group": "<PERSON><PERSON>"}, {"label": "Bitter", "value": "bitter", "group": "<PERSON><PERSON>"}, {"label": "Brygada 1918", "value": "brygada1918", "group": "<PERSON><PERSON>"}, {"label": "Domine", "value": "domine", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "faustina", "group": "<PERSON><PERSON>"}, {"label": "<PERSON>", "value": "frankruhllibre", "group": "<PERSON><PERSON>"}, {"label": "Imbue", "value": "imbue", "group": "<PERSON><PERSON>"}, {"label": "K<PERSON><PERSON>", "value": "kreon", "group": "<PERSON><PERSON>"}, {"label": "Labrada", "value": "labrada", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "lora", "group": "<PERSON><PERSON>"}, {"label": "Manuale", "value": "manuale", "group": "<PERSON><PERSON>"}, {"label": "Petrona", "value": "petrona", "group": "<PERSON><PERSON>"}, {"label": "Playfair Display", "value": "playfairdisplay", "group": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON> Slab", "value": "robotoslab", "group": "<PERSON><PERSON>"}, {"label": "Rokkitt", "value": "rokkitt", "group": "<PERSON><PERSON>"}, {"label": "Yrsa", "value": "yrsa", "group": "<PERSON><PERSON>"}, {"label": "Advent Pro", "value": "adventpro", "group": "Sans Serif"}, {"label": "Antonio", "value": "antonio", "group": "Sans Serif"}, {"label": "Archivo Narrow", "value": "archivonarrow", "group": "Sans Serif"}, {"label": "Asap", "value": "asap", "group": "Sans Serif"}, {"label": "Assistant", "value": "assistant", "group": "Sans Serif"}, {"label": "Cabin", "value": "cabin", "group": "Sans Serif"}, {"label": "Cairo", "value": "cairo", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "dosis", "group": "Sans Serif"}, {"label": "Exo", "value": "exo", "group": "Sans Serif"}, {"label": "Figtree", "value": "figtree", "group": "Sans Serif"}, {"label": "Glory", "value": "glory", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "he<PERSON>o", "group": "Sans Serif"}, {"label": "Instrument Sans", "value": "instrumentsans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "jura", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "karla", "group": "Sans Serif"}, {"label": "Lexend", "value": "lexend", "group": "Sans Serif"}, {"label": "Libre Franklin", "value": "librefranklin", "group": "Sans Serif"}, {"label": "Manrope", "value": "manrope", "group": "Sans Serif"}, {"label": "Maven <PERSON>", "value": "mavenpro", "group": "Sans Serif"}, {"label": "Merriweather Sans", "value": "merriweather<PERSON>s", "group": "Sans Serif"}, {"label": "Montserrat", "value": "montserrat", "group": "Sans Serif"}, {"label": "Nunito", "value": "nunito", "group": "Sans Serif"}, {"label": "Orbitron", "value": "orbitron", "group": "Sans Serif"}, {"label": "<PERSON>", "value": "oswald", "group": "Sans Serif"}, {"label": "Plus Jakarta Sans", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "Sans Serif"}, {"label": "Public Sans", "value": "publicsans", "group": "Sans Serif"}, {"label": "Quicksand", "value": "quicksand", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "raleway", "group": "Sans Serif"}, {"label": "Red Hat Display", "value": "redhatdisplay", "group": "Sans Serif"}, {"label": "Roboto Flex", "value": "robotoflex", "group": "Sans Serif"}, {"label": "<PERSON><PERSON><PERSON>", "value": "rubik", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "ruda", "group": "Sans Serif"}, {"label": "Smooch Sans", "value": "smoochsans", "group": "Sans Serif"}, {"label": "Spartan", "value": "spartan", "group": "Sans Serif"}, {"label": "Urbanist", "value": "urbanist", "group": "Sans Serif"}, {"label": "Work Sans", "value": "worksans", "group": "Sans Serif"}, {"label": "<PERSON><PERSON>", "value": "yanonekaffeesatz", "group": "Sans Serif"}, {"label": "Big Shoulders Display", "value": "bigshouldersdisplay", "group": "Cursive"}, {"label": "Comfortaa", "value": "comfortaa", "group": "Cursive"}, {"label": "<PERSON>ript", "value": "dancingscript", "group": "Cursive"}, {"label": "Gluten", "value": "gluten", "group": "Cursive"}, {"label": "Lemonada", "value": "lemonada", "group": "Cursive"}, {"label": "JetBrains Mono", "value": "jetbrainsmono", "group": "Monospace"}, {"label": "Red Hat Mono", "value": "redhatmon<PERSON>", "group": "Monospace"}, {"label": "Source Code Pro", "value": "sourcecodepro", "group": "Monospace"}]}, {"name": "disableFontHeadingsItalic", "label": "Disable italic style", "group": "Fonts", "note": "This option allows you to disable loading of the dedicated italic version for the headings font. The italic font is automatically loaded when selecting a headings font that supports it, like <PERSON>ra, for optimal appearance. However, for performance reasons, you can choose to prevent loading of the dedicated italic version by enabling this option.", "type": "checkbox", "value": false, "dependencies": [{"field": "fontHeadings", "value": "adventpro,aleo,andadapro,archivonarrow,asap,besley,bitter,brygada1918,cabin,exo,faustina,figtree,glory,instrumentsans,jetbrainsmono,karla,labrada,librefranklin,lora,manuale,merriweathersans,montserrat,nunito,petrona,playfairdisplay,plusjakartasans,publicsans,raleway,redhatdisplay,redhatmono,rokkitt,rubik,sourcecodepro,urbanist,worksans,yrsa"}]}, {"name": "fontMenu", "label": "Menu font", "group": "Fonts", "value": "var(--body-font)", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui"}, {"label": "Select the font used for the Body", "value": "var(--body-font)"}, {"label": "Select the font used for the Headings", "value": "var(--heading-font)"}]}, {"name": "fontLogo", "label": "Logo font", "group": "Fonts", "value": "var(--body-font)", "type": "select", "options": [{"label": "OS Default Font", "value": "system-ui"}, {"label": "Select the font used for the Body", "value": "var(--body-font)"}, {"label": "Select the font used for the Headings", "value": "var(--heading-font)"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Fonts", "size": "big thin"}, {"name": "minFontSize", "label": "Minimum font size", "group": "Fonts", "note": "The font-size of the root element in REM unit", "value": "1.1", "type": "range", "min": 1, "max": 3, "step": 0.05}, {"name": "maxFontSize", "label": "Maximum font size", "group": "Fonts", "note": "The font-size of the root element in REM unit", "value": "1.2", "type": "range", "min": 1, "max": 3, "step": 0.05}, {"name": "lineHeight", "label": "Line height", "group": "Fonts", "note": "The default line-height for text within body elements, excluding headings.", "value": "1.7", "type": "range", "min": 1, "max": 3, "step": 0.05}, {"name": "letterSpacing", "label": "Letter spacing", "group": "Fonts", "note": "Adjusts the spacing between characters for body text elements, excluding headings, in EM.", "value": "0", "type": "range", "min": -1, "max": 1, "step": 0.01}, {"name": "separator", "type": "separator", "label": "", "group": "Fonts", "note": "Note that not all fonts support the full range of weights; instead, they will typically support a standard range between 400 and 700. To see the exact range available for the selected body font, please visit our <a href='https://getpublii.com/docs/fonts.html' target='_blank'>documentation</a>.", "size": "small thin"}, {"name": "fontBodyWeight", "label": "Normal font weight", "group": "Fonts", "value": "400", "type": "range", "min": 100, "max": 900, "step": 1}, {"name": "fontBoldWeight", "label": "Bold font weight", "group": "Fonts", "value": "600", "type": "range", "min": 100, "max": 900, "step": 1}, {"name": "separator", "type": "separator", "label": "Headings", "group": "Fonts", "size": "big"}, {"name": "fontHeadignsWeight", "label": "H1-H6 font weight", "group": "Fonts", "value": "500", "type": "range", "min": 100, "max": 900, "step": 1}, {"name": "fontHeadingsStyle", "label": "H1-H6 font style", "group": "Fonts", "value": "normal", "type": "select", "options": [{"label": "Normal", "value": "normal"}, {"label": "Italic", "value": "italic"}, {"label": "Oblique", "value": "oblique"}]}, {"name": "fontHeadingsLineHeight", "label": "H1-H6 line height", "group": "Fonts", "note": "The default line-height for heading elements (h1, h2, h3, etc.).", "value": "1.2", "type": "range", "min": 1, "max": 3, "step": 0.05}, {"name": "fontHeadingsletterSpacing", "label": "H1-H6 letter spacing", "group": "Fonts", "note": "Adjusts the spacing between characters for heading elements (h1, h2, h3, etc.) in EM.", "value": "0", "type": "range", "min": -1, "max": 1, "step": 0.01}, {"name": "fontHeadingsTransform", "label": "H1-H6 text transform", "group": "Fonts", "value": "none", "type": "select", "options": [{"label": "None", "value": "none"}, {"label": "Capitalize", "value": "capitalize"}, {"label": "Lowercase", "value": "lowercase"}, {"label": "Uppercase", "value": "uppercase"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Share Buttons", "note": "This section allows you to choose your preferred share buttons for various platforms. If you wish to add more, you can install the <a href='https://marketplace.getpublii.com/plugins/social-sharing/' target='_blank'>Social Sharing plugin</a> available on the Publii Marketplace.", "size": "no-line"}, {"name": "shareFacebook", "group": "Share Buttons", "label": "Facebook", "value": false, "type": "checkbox"}, {"name": "shareTwitter", "group": "Share Buttons", "label": "X (formerly Twitter)", "value": false, "type": "checkbox"}, {"name": "shareTwitterName", "group": "Share Buttons", "note": "Enter your X account's handle here; it will be used when the X share button is clicked on your site e.g. 'via @XHandle'. If left blank, the default username of @SiteName will be used", "value": "", "type": "text", "dependencies": [{"field": "shareTwitter", "value": "true"}]}, {"name": "sharePinterest", "group": "Share Buttons", "label": "Pinterest", "value": false, "type": "checkbox"}, {"name": "shareMix", "group": "Share Buttons", "label": "Mix (formerly StumbleUpon)", "value": false, "type": "checkbox"}, {"name": "shareLinkedin", "group": "Share Buttons", "label": "LinkedIn", "value": false, "type": "checkbox"}, {"name": "shareBuffer", "group": "Share Buttons", "label": "<PERSON><PERSON><PERSON>", "value": false, "type": "checkbox"}, {"name": "shareWhatsApp", "group": "Share Buttons", "label": "WhatsApp", "value": false, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "", "group": "Footer", "note": "This section allows you to add follow buttons for various social media platforms. If you want to expand the available options, you can install the <a href='https://marketplace.getpublii.com/plugins/follow-buttons/' target='_blank'>Follow Buttons plugin</a> available on the Publii Marketplace.", "size": "no-line"}, {"name": "socialButtons", "group": "Footer", "label": "Follow <PERSON><PERSON>", "value": false, "type": "checkbox"}, {"name": "socialFacebook", "label": "Facebook", "group": "Footer", "placeholder": "Please enter your Facebook URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialTwitter", "label": "X (formerly Twitter)", "group": "Footer", "placeholder": "Please enter your X URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialInstagram", "label": "Instagram", "group": "Footer", "placeholder": "Please enter your Instagram URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialLinkedin", "label": "LinkedIn", "group": "Footer", "placeholder": "Please enter your LinkedIn URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialVimeo", "label": "Vimeo", "group": "Footer", "placeholder": "Please enter your Vimeo URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialPinterest", "label": "Pinterest", "group": "Footer", "placeholder": "Please enter your Pinterest URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "socialYoutube", "label": "Youtube", "group": "Footer", "placeholder": "Please enter your Youtube URL", "value": "", "type": "text", "dependencies": [{"field": "socialButtons", "value": "true"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Footer", "size": "small thin"}, {"name": "copyrightText", "label": "Copyright text", "group": "Footer", "value": "Powered by <PERSON><PERSON><PERSON>", "type": "wysiwyg"}, {"name": "searchFeature", "label": "Search", "group": "Search", "note": "Enable / disable the search box, which is usually located next to the top menu. In order to use the search function, you also need to install the search plugin.", "value": false, "type": "checkbox"}, {"name": "createSearchPage", "label": "Search subpage", "group": "Search", "note": "Enabling this option will create an additional search.html page which may be required by some search plugins to display the search results on a separate page.", "value": false, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "", "group": "Gallery", "note": "To use the gallery functionality, you need to install the appropriate plugin. Please check the available gallery plugins in the <a rel=\"noopener noreferrer\" href=\"https://marketplace.getpublii.com/plugins/\" target=\"_blank\">Publii Marketplace</a>.", "size": "no-line"}, {"name": "galleryItemGap", "label": "Gallery item spacing", "group": "Gallery", "note": "You can use the --baseline variable and multiply it, but you can also use other values like px or rem.", "value": "calc(var(--baseline) * 1.5)", "type": "text"}, {"name": "backToTopButton", "group": "Additional", "label": "\"Back to top\" button", "value": true, "type": "checkbox"}, {"name": "separator", "type": "separator", "label": "", "group": "Additional", "size": "small"}, {"name": "formatDate", "label": "Date format", "group": "Additional", "value": "MMMM D, YYYY", "type": "select", "options": [{"label": "Nov 1, 2016", "value": "MMM D, YYYY"}, {"label": "1 Nov 2016", "value": "D MMM YYYY"}, {"label": "November 1, 2016", "value": "MMMM D, YYYY"}, {"label": "1 November 2016", "value": "D MMMM YYYY"}, {"label": "Sunday, November 1, 2016", "value": "dddd, MMMM D, YYYY"}, {"label": "Sunday, 1 November 2016", "value": "dddd, D MMMM YYYY"}, {"label": "November 1, 2016 10:02:05", "value": "MMMM D, YYYY HH:mm:ss"}, {"label": "1 November 2016 10:02:05", "value": "D MMMM YYYY HH:mm:ss"}, {"label": "01/21/2016", "value": "MM/DD/YYYY"}, {"label": "21/01/2016", "value": "DD/MM/YYYY"}, {"label": "Custom - create your own date format", "value": "custom"}]}, {"name": "formatDateCustom", "group": "Additional", "label": "Create a custom date format", "note": "More details you can find <a href='https://github.com/taylorhakes/fecha' target='_blank'>here.</a>", "value": "HH:mm:ss YY/MM/DD", "type": "text", "dependencies": [{"field": "formatDate", "value": "custom"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Additional", "size": "small"}, {"name": "lazyLoadEffect", "label": "Lazy load effects", "group": "Additional", "note": "This option works only if the 'Media lazy load' option is enabled in the Website Speed tab under Site Settings", "value": "fadein", "type": "select", "options": [{"label": "Fade in", "value": "fadein"}, {"label": "None", "value": "none"}]}, {"name": "separator", "type": "separator", "label": "", "group": "Additional", "size": "small"}, {"name": "faviconUpload", "label": "Upload favicon file", "group": "Additional", "note": "The ideal size of a favicon is 16x16 pixels. Save your favicon as either favicon.png or favicon.ico", "value": "", "type": "smallupload", "upload": true}, {"name": "faviconExtension", "label": "Favicon extension", "group": "Additional", "value": "image/x-icon", "type": "select", "options": [{"label": ".ico", "value": "image/x-icon"}, {"label": ".png", "value": "image/png"}]}], "postConfig": [{"name": "displayDate", "label": "Display date", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "additionalImage", "label": "Additional image", "value": 1, "type": "image"}, {"name": "degignation", "label": "Degignation", "value": "", "type": "text"}, {"name": "mobileNumber", "label": "Mobile Number", "value": 1, "type": "text", "postTemplates": "team"}, {"name": "telephoneNumber", "label": "Telephone Number", "value": 1, "type": "text", "postTemplates": "team"}, {"name": "emailAddress", "label": "Email Address", "value": 1, "type": "text", "postTemplates": "team"}, {"name": "linkedin", "label": "LinkedIn", "value": 1, "type": "text", "postTemplates": "team"}, {"name": "sectorsExperience", "label": "Sectors Experience", "value": 1, "type": "textarea", "postTemplates": "team"}, {"name": "education", "label": "Education", "value": 1, "type": "textarea", "postTemplates": "team"}, {"name": "admission", "label": "Admission", "value": 1, "type": "textarea", "postTemplates": "team"}, {"name": "languages", "label": "Languages", "value": 1, "type": "textarea", "postTemplates": "team"}, {"name": "membership", "label": "Membership", "value": 1, "type": "textarea", "postTemplates": "team"}, {"name": "category", "label": "Category", "value": 1, "type": "text"}, {"name": "displayAuthor", "label": "Display author", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayLastUpdatedDate", "label": "Display last updated date", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayTags", "label": "Display tags", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayShareButtons", "label": "Display share buttons", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayAuthorBio", "label": "Display author bio", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostNavigation", "label": "Display post navigation", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayRelatedPosts", "label": "Display related posts", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayComments", "label": "Display comments", "value": 0, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "pageConfig": [{"name": "displayDate", "label": "Display date", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayAuthor", "label": "Display author", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayLastUpdatedDate", "label": "Display last updated date", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayShareButtons", "label": "Display share buttons", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayAuthorBio", "label": "Display author bio", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayChildPages", "label": "Display child pages", "value": 0, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayComments", "label": "Display comments", "value": 0, "type": "select", "pageTemplates": "!empty", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "authorConfig": [{"name": "displayFeaturedImage", "label": "Display featured image", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayAvatar", "label": "Display avatar", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostCounter", "label": "Display post counter", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayDescription", "label": "Display author biography", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayWebsite", "label": "Display author website", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostList", "label": "Display post list", "note": "Disabling the post list can cause duplicate content issues. To avoid this, please disable authors pagination in the SEO section of your site settings.", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "tagConfig": [{"name": "displayFeaturedImage", "label": "Display featured image", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostCounter", "label": "Display post counter", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayDescription", "label": "Display tag description", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}, {"name": "displayPostList", "label": "Display post list", "note": "Disabling the post list can cause duplicate content issues. To avoid this, please disable tag pagination in the SEO section of your site settings.", "value": 1, "type": "select", "options": [{"label": "Enabled", "value": 1}, {"label": "Disabled", "value": 0}]}], "files": {"ignoreAssets": ["scss", ".DS_Store"], "assetsPath": "assets", "useDynamicAssets": true, "partialsPath": "partials", "responsiveImages": {"contentImages": {"sizes": "(max-width: 1920px) 100vw, 1920px", "dimensions": {"xs": {"width": 640, "height": "auto"}, "sm": {"width": 768, "height": "auto"}, "md": {"width": 1024, "height": "auto"}, "lg": {"width": 1366, "height": "auto"}, "xl": {"width": 1600, "height": "auto"}, "2xl": {"width": 1920, "height": "auto"}}}, "featuredImages": {"sizes": {"hero": "88vw", "feed": "(min-width: 600px) calc(4.38vw + 143px), 87.86vw"}, "dimensions": {"xs": {"width": 640, "height": "auto", "group": "hero,feed"}, "sm": {"width": 768, "height": "auto", "group": "hero,feed"}, "md": {"width": 1024, "height": "auto", "group": "hero,feed"}, "lg": {"width": 1366, "height": "auto", "group": "hero"}, "xl": {"width": 1600, "height": "auto", "group": "hero"}, "2xl": {"width": 1920, "height": "auto", "group": "hero"}}}, "tagImages": {"sizes": "88vw", "dimensions": {"xs": {"width": 640, "height": "auto"}, "sm": {"width": 768, "height": "auto"}, "md": {"width": 1024, "height": "auto"}, "lg": {"width": 1366, "height": "auto"}, "xl": {"width": 1600, "height": "auto"}, "2xl": {"width": 1920, "height": "auto"}}}, "authorImages": {"sizes": "88vw", "dimensions": {"xs": {"width": 640, "height": "auto"}, "sm": {"width": 768, "height": "auto"}, "md": {"width": 1024, "height": "auto"}, "lg": {"width": 1366, "height": "auto"}, "xl": {"width": 1600, "height": "auto"}, "2xl": {"width": 1920, "height": "auto"}}}, "optionImages": {"sizes": "88vw", "dimensions": {"xs": {"width": 640, "height": "auto"}, "sm": {"width": 768, "height": "auto"}, "md": {"width": 1024, "height": "auto"}, "lg": {"width": 1366, "height": "auto"}, "xl": {"width": 1600, "height": "auto"}, "2xl": {"width": 1920, "height": "auto"}}}, "galleryImages": {"sizes": "", "dimensions": {"thumbnail": {"width": 720, "height": "auto"}}}}}, "customElements": [{"label": "Ordered list", "cssClasses": "ordered-list", "selector": "ol"}, {"label": "Drop cap", "cssClasses": "dropcap", "selector": "p"}, {"label": "Info", "cssClasses": "msg msg--info", "selector": "p"}, {"label": "Highlight", "cssClasses": "msg msg--highlight ", "selector": "p"}, {"label": "Success", "cssClasses": "msg msg--success", "selector": "p"}, {"label": "Warning", "cssClasses": "msg msg--warning", "selector": "p"}, {"label": "Table bordered", "cssClasses": "table-bordered", "selector": "table"}, {"label": "Table striped", "cssClasses": "table-striped", "selector": "table"}, {"label": "Table title", "cssClasses": "table-title", "selector": "table"}], "postTypeHelp": {"team": {"description": "Use this template for team member profiles. Add team member details in the Additional Data section.", "requiredFields": ["position", "bio"], "optionalFields": ["social_links", "skills"], "tags": ["Team"]}, "blog": {"description": "Enhanced blog post template with category, reading time, and featured post options.", "requiredFields": [], "optionalFields": ["category", "reading_time", "featured"], "tags": ["Blog"]}}}