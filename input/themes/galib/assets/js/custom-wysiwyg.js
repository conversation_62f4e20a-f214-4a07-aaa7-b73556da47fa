document.addEventListener('DOMContentLoaded', function () {
    // Wait until Publii's UI renders
    setTimeout(() => {
        const textarea = document.querySelector('textarea[name="sectorsExperience"]');
        if (textarea && !textarea.classList.contains('wysiwyg-enabled')) {
            textarea.classList.add('wysiwyg-enabled');

            // Load TinyMCE if not already loaded
            if (typeof tinymce === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js';
                script.referrerPolicy = 'origin';
                script.onload = () => initTinyMCE(textarea);
                document.head.appendChild(script);
            } else {
                initTinyMCE(textarea);
            }
        }
    }, 500);
});

function initTinyMCE(textarea) {
    tinymce.init({
        target: textarea,
        menubar: false,
        toolbar: 'bold italic underline | bullist numlist | link unlink | code',
        plugins: 'lists link code',
        height: 200
    });
}
