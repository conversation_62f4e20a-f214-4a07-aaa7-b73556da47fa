@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");
/* Grid variables */
:root{
  --gutter-x: 1rem;
  --gutter-y: 1rem;
  --columns: 12;
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
}

/* Containers */
.container {
  width: 100%;
  padding-right: calc(var(--gutter-x)/2);
  padding-left: calc(var(--gutter-x)/2);
  margin-right: auto;
  margin-left: auto;
  box-sizing: border-box;
}
@media (min-width: 576px){ .container { max-width: var(--container-sm); } }
@media (min-width: 768px){ .container { max-width: var(--container-md); } }
@media (min-width: 992px){ .container { max-width: var(--container-lg); } }
@media (min-width: 1200px){ .container { max-width: var(--container-xl); } }
@media (min-width: 1400px){ .container { max-width: var(--container-xxl); } }

/* Fluid container */
.container-fluid { width:100%; padding-right: calc(var(--gutter-x)/2); padding-left: calc(var(--gutter-x)/2); box-sizing:border-box; }

/* Row */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--gutter-x)/-2);
  margin-left: calc(var(--gutter-x)/-2);
  gap: var(--gutter-y);
  box-sizing: border-box;
}

/* Column base */
.col {
  flex: 1 0 0%;
  min-width: 0;
  padding-right: calc(var(--gutter-x)/2);
  padding-left: calc(var(--gutter-x)/2);
  box-sizing: border-box;
}

/* Auto-width column */
.col-auto {
  flex: 0 0 auto;
  width: auto;
}

/* Full-width (equal-width) columns: if you want fixed-percentage columns, use .col-{n} classes below */

/* Utility to calculate percentage width for n of 12 */
:root { --_col-unit: calc(100% / var(--columns)); }

/* Generate .col-1 .. .col-12 */
.col-1  { flex: 0 0 calc(var(--_col-unit) * 1); max-width: calc(var(--_col-unit) * 1); }
.col-2  { flex: 0 0 calc(var(--_col-unit) * 2); max-width: calc(var(--_col-unit) * 2); }
.col-3  { flex: 0 0 calc(var(--_col-unit) * 3); max-width: calc(var(--_col-unit) * 3); }
.col-4  { flex: 0 0 calc(var(--_col-unit) * 4); max-width: calc(var(--_col-unit) * 4); }
.col-5  { flex: 0 0 calc(var(--_col-unit) * 5); max-width: calc(var(--_col-unit) * 5); }
.col-6  { flex: 0 0 calc(var(--_col-unit) * 6); max-width: calc(var(--_col-unit) * 6); }
.col-7  { flex: 0 0 calc(var(--_col-unit) * 7); max-width: calc(var(--_col-unit) * 7); }
.col-8  { flex: 0 0 calc(var(--_col-unit) * 8); max-width: calc(var(--_col-unit) * 8); }
.col-9  { flex: 0 0 calc(var(--_col-unit) * 9); max-width: calc(var(--_col-unit) * 9); }
.col-10 { flex: 0 0 calc(var(--_col-unit) * 10); max-width: calc(var(--_col-unit) * 10); }
.col-11 { flex: 0 0 calc(var(--_col-unit) * 11); max-width: calc(var(--_col-unit) * 11); }
.col-12 { flex: 0 0 calc(var(--_col-unit) * 12); max-width: calc(var(--_col-unit) * 12); }

/* Offsets (optional) */
.offset-1  { margin-left: calc(var(--_col-unit) * 1); }
.offset-2  { margin-left: calc(var(--_col-unit) * 2); }
.offset-3  { margin-left: calc(var(--_col-unit) * 3); }
.offset-4  { margin-left: calc(var(--_col-unit) * 4); }
.offset-5  { margin-left: calc(var(--_col-unit) * 5); }
.offset-6  { margin-left: calc(var(--_col-unit) * 6); }
.offset-7  { margin-left: calc(var(--_col-unit) * 7); }
.offset-8  { margin-left: calc(var(--_col-unit) * 8); }
.offset-9  { margin-left: calc(var(--_col-unit) * 9); }
.offset-10 { margin-left: calc(var(--_col-unit) * 10); }
.offset-11 { margin-left: calc(var(--_col-unit) * 11); }

/* Responsive breakpoints: sm (576), md (768), lg (992), xl (1200), xxl (1400) */
/* Repeat .col-{n} classes inside media queries for responsive variants: .col-sm-*, .col-md-*, .col-lg-*, .col-xl-*, .col-xxl-* */

@media (min-width: 576px) {
  .col-sm-1  { flex: 0 0 calc(var(--_col-unit) * 1);  max-width: calc(var(--_col-unit) * 1); }
  .col-sm-2  { flex: 0 0 calc(var(--_col-unit) * 2);  max-width: calc(var(--_col-unit) * 2); }
  .col-sm-3  { flex: 0 0 calc(var(--_col-unit) * 3);  max-width: calc(var(--_col-unit) * 3); }
  .col-sm-4  { flex: 0 0 calc(var(--_col-unit) * 4);  max-width: calc(var(--_col-unit) * 4); }
  .col-sm-5  { flex: 0 0 calc(var(--_col-unit) * 5);  max-width: calc(var(--_col-unit) * 5); }
  .col-sm-6  { flex: 0 0 calc(var(--_col-unit) * 6);  max-width: calc(var(--_col-unit) * 6); }
  .col-sm-7  { flex: 0 0 calc(var(--_col-unit) * 7);  max-width: calc(var(--_col-unit) * 7); }
  .col-sm-8  { flex: 0 0 calc(var(--_col-unit) * 8);  max-width: calc(var(--_col-unit) * 8); }
  .col-sm-9  { flex: 0 0 calc(var(--_col-unit) * 9);  max-width: calc(var(--_col-unit) * 9); }
  .col-sm-10 { flex: 0 0 calc(var(--_col-unit) * 10); max-width: calc(var(--_col-unit) * 10); }
  .col-sm-11 { flex: 0 0 calc(var(--_col-unit) * 11); max-width: calc(var(--_col-unit) * 11); }
  .col-sm-12 { flex: 0 0 calc(var(--_col-unit) * 12); max-width: calc(var(--_col-unit) * 12); }
}

@media (min-width: 768px) {
  .col-md-1  { flex: 0 0 calc(var(--_col-unit) * 1);  max-width: calc(var(--_col-unit) * 1); }
  .col-md-2  { flex: 0 0 calc(var(--_col-unit) * 2);  max-width: calc(var(--_col-unit) * 2); }
  .col-md-3  { flex: 0 0 calc(var(--_col-unit) * 3);  max-width: calc(var(--_col-unit) * 3); }
  .col-md-4  { flex: 0 0 calc(var(--_col-unit) * 4);  max-width: calc(var(--_col-unit) * 4); }
  .col-md-5  { flex: 0 0 calc(var(--_col-unit) * 5);  max-width: calc(var(--_col-unit) * 5); }
  .col-md-6  { flex: 0 0 calc(var(--_col-unit) * 6);  max-width: calc(var(--_col-unit) * 6); }
  .col-md-7  { flex: 0 0 calc(var(--_col-unit) * 7);  max-width: calc(var(--_col-unit) * 7); }
  .col-md-8  { flex: 0 0 calc(var(--_col-unit) * 8);  max-width: calc(var(--_col-unit) * 8); }
  .col-md-9  { flex: 0 0 calc(var(--_col-unit) * 9);  max-width: calc(var(--_col-unit) * 9); }
  .col-md-10 { flex: 0 0 calc(var(--_col-unit) * 10); max-width: calc(var(--_col-unit) * 10); }
  .col-md-11 { flex: 0 0 calc(var(--_col-unit) * 11); max-width: calc(var(--_col-unit) * 11); }
  .col-md-12 { flex: 0 0 calc(var(--_col-unit) * 12); max-width: calc(var(--_col-unit) * 12); }
}

@media (min-width: 992px) {
  .col-lg-1  { flex: 0 0 calc(var(--_col-unit) * 1);  max-width: calc(var(--_col-unit) * 1); }
  .col-lg-2  { flex: 0 0 calc(var(--_col-unit) * 2);  max-width: calc(var(--_col-unit) * 2); }
  .col-lg-3  { flex: 0 0 calc(var(--_col-unit) * 3);  max-width: calc(var(--_col-unit) * 3); }
  .col-lg-4  { flex: 0 0 calc(var(--_col-unit) * 4);  max-width: calc(var(--_col-unit) * 4); }
  .col-lg-5  { flex: 0 0 calc(var(--_col-unit) * 5);  max-width: calc(var(--_col-unit) * 5); }
  .col-lg-6  { flex: 0 0 calc(var(--_col-unit) * 6);  max-width: calc(var(--_col-unit) * 6); }
  .col-lg-7  { flex: 0 0 calc(var(--_col-unit) * 7);  max-width: calc(var(--_col-unit) * 7); }
  .col-lg-8  { flex: 0 0 calc(var(--_col-unit) * 8);  max-width: calc(var(--_col-unit) * 8); }
  .col-lg-9  { flex: 0 0 calc(var(--_col-unit) * 9);  max-width: calc(var(--_col-unit) * 9); }
  .col-lg-10 { flex: 0 0 calc(var(--_col-unit) * 10); max-width: calc(var(--_col-unit) * 10); }
  .col-lg-11 { flex: 0 0 calc(var(--_col-unit) * 11); max-width: calc(var(--_col-unit) * 11); }
  .col-lg-12 { flex: 0 0 calc(var(--_col-unit) * 12); max-width: calc(var(--_col-unit) * 12); }
}

@media (min-width: 1200px) {
  .col-xl-1  { flex: 0 0 calc(var(--_col-unit) * 1);  max-width: calc(var(--_col-unit) * 1); }
  .col-xl-2  { flex: 0 0 calc(var(--_col-unit) * 2);  max-width: calc(var(--_col-unit) * 2); }
  .col-xl-3  { flex: 0 0 calc(var(--_col-unit) * 3);  max-width: calc(var(--_col-unit) * 3); }
  .col-xl-4  { flex: 0 0 calc(var(--_col-unit) * 4);  max-width: calc(var(--_col-unit) * 4); }
  .col-xl-5  { flex: 0 0 calc(var(--_col-unit) * 5);  max-width: calc(var(--_col-unit) * 5); }
  .col-xl-6  { flex: 0 0 calc(var(--_col-unit) * 6);  max-width: calc(var(--_col-unit) * 6); }
  .col-xl-7  { flex: 0 0 calc(var(--_col-unit) * 7);  max-width: calc(var(--_col-unit) * 7); }
  .col-xl-8  { flex: 0 0 calc(var(--_col-unit) * 8);  max-width: calc(var(--_col-unit) * 8); }
  .col-xl-9  { flex: 0 0 calc(var(--_col-unit) * 9);  max-width: calc(var(--_col-unit) * 9); }
  .col-xl-10 { flex: 0 0 calc(var(--_col-unit) * 10); max-width: calc(var(--_col-unit) * 10); }
  .col-xl-11 { flex: 0 0 calc(var(--_col-unit) * 11); max-width: calc(var(--_col-unit) * 11); }
  .col-xl-12 { flex: 0 0 calc(var(--_col-unit) * 12); max-width: calc(var(--_col-unit) * 12); }
}

@media (min-width: 1400px) {
  .col-xxl-1  { flex: 0 0 calc(var(--_col-unit) * 1);  max-width: calc(var(--_col-unit) * 1); }
  .col-xxl-2  { flex: 0 0 calc(var(--_col-unit) * 2);  max-width: calc(var(--_col-unit) * 2); }
  .col-xxl-3  { flex: 0 0 calc(var(--_col-unit) * 3);  max-width: calc(var(--_col-unit) * 3); }
  .col-xxl-4  { flex: 0 0 calc(var(--_col-unit) * 4);  max-width: calc(var(--_col-unit) * 4); }
  .col-xxl-5  { flex: 0 0 calc(var(--_col-unit) * 5);  max-width: calc(var(--_col-unit) * 5); }
  .col-xxl-6  { flex: 0 0 calc(var(--_col-unit) * 6);  max-width: calc(var(--_col-unit) * 6); }
  .col-xxl-7  { flex: 0 0 calc(var(--_col-unit) * 7);  max-width: calc(var(--_col-unit) * 7); }
  .col-xxl-8  { flex: 0 0 calc(var(--_col-unit) * 8);  max-width: calc(var(--_col-unit) * 8); }
  .col-xxl-9  { flex: 0 0 calc(var(--_col-unit) * 9);  max-width: calc(var(--_col-unit) * 9); }
  .col-xxl-10 { flex: 0 0 calc(var(--_col-unit) * 10); max-width: calc(var(--_col-unit) * 10); }
  .col-xxl-11 { flex: 0 0 calc(var(--_col-unit) * 11); max-width: calc(var(--_col-unit) * 11); }
  .col-xxl-12 { flex: 0 0 calc(var(--_col-unit) * 12); max-width: calc(var(--_col-unit) * 12); }
}

/* Alignment helpers */
.align-items-start { align-items: flex-start; }
.align-items-center{ align-items: center; }
.align-items-end   { align-items: flex-end; }
.justify-content-start { justify-content: flex-start; }
.justify-content-center{ justify-content: center; }
.justify-content-end   { justify-content: flex-end; }

/* Small niceties */
.row > [class*="col-"], .row > .col { min-height: 1px; }



:root {
  --white-color: #FFFFFF;
  --title-color: #231F20;
  --black-color: #000;
  --primary-color: #E41F26;
  --font-open-sans: Open Sans;
}

/*================================================
03. Global Css
=================================================*/
.container-one {
  width: 100%;
  max-width: 760px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}

body {
  margin: 0px;
  padding: 0px;
  overflow-x: hidden;
}

html {
  font-size: 100%;
  scroll-behavior: smooth;
}

input {
  border: none;
  outline: none;
}

button {
  outline: none;
  border: none;
}

i.bx {
  vertical-align: middle;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
}

p {
  font-family: var(--font-open-sans);
  font-size: 18px;
  font-weight: 300;
  line-height: 25px;
  color: var(--black-color);
  margin-bottom: 40px;
}

.pt-120 {
  padding-top: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pt-120 {
    padding-top: 100px;
  }
}
@media (max-width: 991px) {
  .pt-120 {
    padding-top: 90px;
  }
}

.pb-120 {
  padding-bottom: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pb-120 {
    padding-bottom: 100px;
  }
}
@media (max-width: 991px) {
  .pb-120 {
    padding-bottom: 90px;
  }
}

.pt-100 {
  padding-top: 100px;
}
@media (max-width: 991px) {
  .pt-100 {
    padding-top: 80px;
  }
}

.pb-100 {
  padding-bottom: 100px;
}
@media (max-width: 991px) {
  .pb-100 {
    padding-bottom: 80px;
  }
}

.pt-90 {
  padding-top: 90px;
}
@media (max-width: 991px) {
  .pt-90 {
    padding-top: 80px;
  }
}
@media (max-width: 767px) {
  .pt-90 {
    padding-top: 70px;
  }
}

.pb-90 {
  padding-bottom: 90px;
}
@media (max-width: 991px) {
  .pb-90 {
    padding-bottom: 80px;
  }
}
@media (max-width: 767px) {
  .pb-90 {
    padding-bottom: 70px;
  }
}

.pb-80 {
  padding-bottom: 80px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pb-80 {
    padding-bottom: 60px;
  }
}

.pb-65 {
  padding-bottom: 65px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-10 {
  padding-bottom: 10px;
}

.mt-120 {
  margin-top: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .mt-120 {
    margin-top: 100px;
  }
}
@media (max-width: 991px) {
  .mt-120 {
    margin-top: 90px;
  }
}

.mb-120 {
  margin-bottom: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .mb-120 {
    margin-bottom: 100px;
  }
}
@media (max-width: 991px) {
  .mb-120 {
    margin-bottom: 90px;
  }
}

.mb-100 {
  margin-bottom: 100px;
}
@media (max-width: 991px) {
  .mb-100 {
    margin-bottom: 80px;
  }
}

.mt-100 {
  margin-top: 100px !important;
}
@media (max-width: 991px) {
  .mt-100 {
    margin-top: 80px !important;
  }
}

.mb-90 {
  margin-bottom: 90px;
}
@media (max-width: 991px) {
  .mb-90 {
    margin-bottom: 70px;
  }
}

.mt-20 {
  margin-top: 20px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-10 {
  margin-bottom: 10px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pe-80 {
  padding-right: 80px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-110 {
  padding-left: 110px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .pl-110 {
    padding-left: 70px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pl-110 {
    padding-left: 40px;
  }
}
@media (max-width: 1199px) {
  .pl-110 {
    padding-left: unset;
  }
}

.mb-60 {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .mb-60 {
    margin-bottom: 40px;
  }
}

.mb-70 {
  margin-bottom: 70px;
}
@media (max-width: 767px) {
  .mb-70 {
    margin-bottom: 40px;
  }
}

.mb-80 {
  margin-bottom: 80px;
}
@media (max-width: 767px) {
  .mb-80 {
    margin-bottom: 50px;
  }
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-44 {
  margin-bottom: 44px;
}
@media (max-width: 991px) {
  .mb-44 {
    margin-bottom: 0px;
  }
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mt-60 {
  margin-top: 60px;
}
@media (max-width: 767px) {
  .mt-60 {
    margin-top: 40px;
  }
}

.mt-70 {
  margin-top: 70px;
}
@media (max-width: 767px) {
  .mt-70 {
    margin-top: 40px;
  }
}

header {
  z-index: 999;
  transition: all 0.8s ease-out 0s;
  width: 100%;
  padding: 55px 0 40px;
}
@media (max-width: 991px) {
  header {
    padding: 25px 15px;
    border-bottom: 1px solid var(--primary-color);
  }
}
header.sticky {
  top: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  background-color: #08090b;
  z-index: 999;
}
@keyframes smooth-header {
  0% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0px);
  }
}
header .header-logo {
  margin-top: -10px;
  display: flex;
  align-items: center;
}
header .header-logo img {
  max-width: 120px;
  width: 100%;
}
@media (max-width: 991px) {
  header .header-logo {
    margin-top: 0;
  }
}
header .header-wrapper {
  max-width: 1015px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  gap: 100px;
  position: relative;
  z-index: 1;
}
header .header-wrapper::after {
  content: "";
  height: 1px;
  width: 755px;
  position: absolute;
  top: 53px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary-color);
  z-index: -1;
}
.navbar__toggle.js-toggle{
  display: none;
}
@media (max-width: 991px) {
  header .header-wrapper::after {
    display: none;
    visibility: hidden;
  }
}
header .header-wrapper .menu-and-social {
  display: flex;
  align-items: center;
  gap: 100px;
}
header .main-menu {
  display: inline-block;
}
header .main-menu .mobile-menu-logo {
  display: none;
}
header .main-menu .navbar__menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
header .main-menu .navbar__menu > li {
  display: inline-block;
  position: relative;
  padding: 26px 0px;
}
header .main-menu .navbar__menu > li > a {
  color: rgba(35, 31, 32, 0.5);
  display: block;
  text-transform: uppercase;
  padding: 0px 7px;
  position: relative;
  font-family: var(--font-open-sans);
  font-weight: 700;
  font-size: 12px;
  transition: all 0.5s ease-out 0s;
  position: relative;
  line-height: 1;
  text-align: left;
}
header .main-menu .navbar__menu > li i {
  font-size: 20px;
  text-align: center;
  color: var(--title-color);
  position: absolute;
  right: -5px;
  top: 5px;
  z-index: 999;
  cursor: pointer;
  display: none;
  transition: all 0.5s ease-out 0s;
  opacity: 0;
}
@media (max-width: 991px) {
  header .main-menu .navbar__menu > li i {
    opacity: 1;
  }
}
header .main-menu .navbar__menu > li i.active::before {
  content: "\f2ea";
}
header .main-menu .navbar__menu > li .mega-menu {
  position: absolute;
  left: 0;
  right: 0;
  top: 64px;
  margin: 0;
  display: none;
  min-width: 300px;
  background: var(--black-color);
  text-align: left;
  padding: 9px 60px 37px 12px;
}
@media (max-width: 991px) {
  header .main-menu .navbar__menu > li .mega-menu {
    position: static;
    min-width: 200px;
    background: 0 0;
    border: none;
    opacity: 1;
    visibility: visible;
    box-shadow: none;
    transform: none;
    transition: none;
    display: none;
    margin-top: 0 !important;
    transform: translateY(0px);
    padding: 10px 0;
    padding-left: 10px;
  }
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 20px;
}
@media (max-width: 991px) {
  header .main-menu .navbar__menu > li .mega-menu .megamenu-row {
    display: block;
  }
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu {
  margin: 0;
  padding: 0;
  list-style: none;
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu > li {
  padding: 0;
  display: block;
  position: relative;
  margin-left: 5px;
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu > li:first-child {
  margin-left: -5px;
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu > li > a {
  display: block;
  padding: 5px 0px;
  font-family: var(--font-open-sans);
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 9px;
  line-height: 1.4;
  transition: all 0.4s ease-out 0s;
  position: relative;
  border-radius: 0;
  border: none;
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu > li > a.active {
  color: var(--white-color);
}
@media (max-width: 991px) {
  header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu > li > a {
    color: var(--title-color);
    font-size: 10px;
  }
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu > li:hover > a {
  color: #fff;
}
header .main-menu .navbar__menu > li .mega-menu .megamenu-row .megamenu-column ul.mega-navbar__submenu > li.active > a {
  color: var(--white-color);
}
header .main-menu .navbar__menu > li ul.navbar__submenu {
  position: absolute;
  left: 0;
  right: 0;
  top: 64px;
  margin: 0;
  display: none;
  min-width: 230px;
  background: var(--black-color);
  text-align: left;
  padding: 9px 70px 37px 12px;
}
header .main-menu .navbar__menu > li ul.navbar__submenu > li {
  padding: 0;
  display: block;
  position: relative;
}
header .main-menu .navbar__menu > li ul.navbar__submenu > li i {
  position: absolute;
  top: 10px;
  right: 6px;
  display: block;
  color: var(--white-color);
  font-size: 20px;
}
header .main-menu .navbar__menu > li ul.navbar__submenu > li > a {
  display: block;
  padding: 5px 0px;
  font-family: var(--font-open-sans);
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 9px;
  line-height: 1.4;
  transition: all 0.4s ease-out 0s;
  position: relative;
  border-radius: 0;
  border: none;
}
header .main-menu .navbar__menu > li ul.navbar__submenu > li > a.active {
  color: var(--white-color);
}
header .main-menu .navbar__menu > li:hover > a {
  color: var(--title-color);
}
@media (max-width: 1199px) {
  header .main-menu .navbar__menu > li:hover > a {
    border: none;
  }
}
@media (min-width: 1200px) {
  header .main-menu .navbar__menu > li:hover > ul.navbar__submenu {
    display: block;
    animation: fade-up 0.45s linear;
  }
}
@media (min-width: 1200px) {
  header .main-menu .navbar__menu > li:hover .mega-menu {
    display: block;
    animation: fade-up 0.45s linear;
  }
}
@keyframes fade-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
    visibility: hidden;
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
    visibility: visible;
  }
}
header .main-menu .navbar__menu > li.active > a {
  color: var(--black-color);
}
header .main-menu .navbar__menu > li.active .dropdown-icon2 {
  color: var(--primary-color);
}
header .main-menu .navbar__menu li.menu-item-has-children > i {
  display: block;
}
header .nav-right{
  display: flex;
  align-items: center;
  gap: 10px;
}
header .nav-right .social-area {
  margin: 0;
  padding: 0;
  list-style: none;
}
header .nav-right .social-area a svg{
  width: 20px;
  height: 20px;
}
header .nav-right .js-search-btn{
  background-color: transparent;
}
@media (max-width: 991px) {
  header .nav-right .social-area {
    display: none;
    visibility: hidden;
  }
}
header .nav-right .mobile-menu-btn {
  display: none;
  visibility: hidden;
}
@media (max-width: 991px) {
  header .nav-right .mobile-menu-btn {
    display: flex;
    margin-left: 30px;
    flex-direction: column;
    align-items: end;
    visibility: visible;
    justify-content: center;
    position: relative;
  }
  header .nav-right .mobile-menu-btn span {
    height: 3px;
    width: 25px;
    background-color: var(--title-color);
    display: flex;
    transition: transform 0.5s ease-in;
    position: absolute;
    top: 0px;
    right: 0;
  }
  header .nav-right .mobile-menu-btn span::before {
    transition-duration: 0.5s;
    position: absolute;
    width: 35px;
    height: 3px;
    background-color: var(--title-color);
    content: "";
    top: -10px;
    right: 0;
  }
  header .nav-right .mobile-menu-btn span::after {
    transition-duration: 0.5s;
    position: absolute;
    width: 15px;
    height: 3px;
    right: 0;
    background-color: var(--title-color);
    content: "";
    top: 10px;
  }
  header .nav-right .mobile-menu-btn.active span {
    background-color: transparent;
  }
  header .nav-right .mobile-menu-btn.active span::before {
    transform: rotateZ(45deg) translate(8px, 6px);
    width: 35px;
  }
  header .nav-right .mobile-menu-btn.active span::after {
    transform: rotateZ(-45deg) translate(8px, -6px);
    width: 35px;
  }
}
@media (max-width: 991px) {
  header .mobile-logo-area {
    justify-content: center !important;
  }
  header .dropdown-icon {
    color: var(--title-color);
    opacity: 1 !important;
  }
  header .header-wrapper .main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    padding: 30px 20px !important;
    z-index: 99999;
    height: 100%;
    overflow: auto;
    background: var(--white-color);
    transform: translateX(-100%);
    transition: transform 0.3s ease-in;
    border-right: 1px solid rgba(13, 23, 32, 0.0784313725);
  }
  header .header-wrapper .main-menu::-webkit-scrollbar {
    width: 0;
  }
  header .header-wrapper .main-menu.show-menu {
    transform: translateX(0);
  }
  header .header-wrapper .main-menu .mobile-menu-logo {
    text-align: left;
    padding-top: 20px;
    display: block;
    padding-bottom: 8px;
  }
  header .header-wrapper .main-menu .navbar__menu {
    padding-top: 50px;
    padding-bottom: 30px;
  }
  header .header-wrapper .main-menu > ul {
    float: none;
    text-align: left;
    padding: 5px 0px 20px 0;
  }
  header .header-wrapper .main-menu > ul > li {
    display: block;
    position: relative;
    padding: 5px 5px;
  }
  header .header-wrapper .main-menu > ul > li:first-child {
    padding-left: 5px;
  }
  header .header-wrapper .main-menu > ul > li a {
    padding: 10px 0;
    display: block;
    color: var(--title-color);
  }
  header .header-wrapper .main-menu > ul > li ul.navbar__submenu {
    position: static;
    min-width: 200px;
    background: 0 0;
    border: none;
    opacity: 1;
    visibility: visible;
    box-shadow: none;
    transform: none;
    transition: none;
    display: none;
    margin-top: 0 !important;
    transform: translateY(0px);
    padding: 10px 0;
    padding-left: 10px;
  }
  header .header-wrapper .main-menu > ul > li ul.navbar__submenu > li a {
    padding: 7px 0px;
    color: var(--title-color);
    font-size: 10px;
  }
  header .header-wrapper .main-menu > ul > li ul.navbar__submenu > li a.active {
    color: var(--white-color);
  }
}

.wrapper {
  margin: 0px auto;
  overflow: hidden;
  position: relative;
}

.line {
  height: 1px;
  width: 365px;
  display: block;
  background-color: var(--primary-color);
}
@media (max-width: 576px) {
  .line {
    width: 220px;
  }
}

.banner-section {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}
@media (max-width: 991px) {
  .banner-section {
    background-attachment: unset;
  }
}
.banner-section.team {
  background-position: 62%;
}
.banner-section .banner-title {
  padding-top: 125px;
  margin-bottom: 45px;
}
@media (max-width: 991px) {
  .banner-section .banner-title {
    padding-top: 150px;
  }
}
.banner-section .banner-title h1 {
  font-family: var(--font-open-sans);
  font-size: 68px;
  font-weight: 300;
  line-height: 64px;
  text-align: left;
  color: var(--white-color);
  padding-top: 10px;
}
@media (max-width: 767px) {
  .banner-section .banner-title h1 {
    font-size: 50px;
    line-height: 50px;
  }
}
.banner-section .banner-content-warp {
  background-color: rgba(255, 255, 255, 0.68);
  padding: 30px 0;
}
.banner-section .banner-content-warp p {
  padding-top: 20px;
}
.banner-section .single-people-content {
  padding: 125px 0 70px;
}
@media (max-width: 991px) {
  .banner-section .single-people-content {
    padding: 150px 0 70px;
  }
}
.banner-section .single-people-content .name-deg {
  padding-top: 10px;
  margin-bottom: 140px;
}
.banner-section .single-people-content .name-deg h3 {
  font-family: var(--font-open-sans);
  font-size: 27px;
  font-weight: 700;
  line-height: 32px;
  color: var(--black-color);
}
.banner-section .single-people-content .name-deg h3 span {
  font-weight: 300;
}
.banner-section .single-people-content .name-deg > span {
  font-family: var(--font-open-sans);
  font-size: 27px;
  font-weight: 300;
  line-height: 32px;
  color: #808285;
}
.banner-section .single-people-content .contact-area {
  line-height: 0;
}
.banner-section .single-people-content .contact-area h6 {
  font-family: var(--font-open-sans);
  font-size: 13px;
  font-weight: 700;
  line-height: 32px;
  color: var(--black-color);
  margin-bottom: 0;
}
.banner-section .single-people-content .contact-area .contact-info {
  margin: 0;
  padding: 0;
  list-style: none;
  line-height: 0;
}
.banner-section .single-people-content .contact-area .contact-info li {
  line-height: 0;
}
.banner-section .single-people-content .contact-area .contact-info li a {
  font-family: var(--font-open-sans);
  font-size: 13px;
  font-weight: 300;
  line-height: 18px;
  color: var(--black-color);
}
.banner-section .single-people-content .contact-area .contact-info li:last-child a {
  transition: 0.4s;
}
.banner-section .single-people-content .contact-area .contact-info li:last-child a:hover {
  text-decoration: underline;
}
.banner-section .single-people-content .contact-area .card-info {
  margin: 0;
  padding: 0;
  list-style: none;
  padding-top: 12px;
}
.banner-section .single-people-content .contact-area .card-info li {
  line-height: 0;
  margin-bottom: 5px;
}
.banner-section .single-people-content .contact-area .card-info li:last-child {
  margin-bottom: 0;
}
.banner-section .single-people-content .contact-area .card-info li a {
  display: flex;
  align-items: center;
  gap: 5px;
  font-family: var(--font-open-sans);
  font-size: 13px;
  font-weight: 300;
  line-height: 18px;
  color: var(--black-color);
  transition: 0.4s;
}
.banner-section .single-people-content .contact-area .card-info li a:hover {
  text-decoration: underline;
}
.banner-section.single-insight {
  min-height: 570px;
}

.sm-news-insignt-sec {
  background-color: var(--primary-color);
}
.sm-news-insignt-sec .news-insight-wrap {
  display: flex;
  align-items: center;
}
@media (max-width: 991px) {
  .sm-news-insignt-sec .news-insight-wrap {
    flex-wrap: wrap;
    gap: 15px;
  }
}
@media (max-width: 576px) {
  .sm-news-insignt-sec .news-insight-wrap {
    padding-bottom: 15px;
  }
}
.sm-news-insignt-sec .news-insight-wrap .title {
  font-family: var(--font-open-sans);
  font-size: 12px;
  font-weight: 700;
  line-height: 16.34px;
  color: var(--white-color);
  margin-bottom: 0;
  padding-right: 55px;
  white-space: nowrap;
}
@media (max-width: 1199px) {
  .sm-news-insignt-sec .news-insight-wrap .title {
    padding-right: 25px;
  }
}
@media (max-width: 576px) {
  .sm-news-insignt-sec .news-insight-wrap .title {
    padding-top: 15px;
  }
}
.sm-news-insignt-sec .news-insight-wrap .news-card-group {
  display: flex;
  align-items: center;
  gap: 15px;
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news {
  display: flex;
  align-items: center;
  gap: 7px;
  min-width: 390px;
}
@media (max-width: 1199px) {
  .sm-news-insignt-sec .news-insight-wrap .single-sm-news {
    min-width: 365px;
  }
}
@media (max-width: 991px) {
  .sm-news-insignt-sec .news-insight-wrap .single-sm-news {
    min-width: unset;
    display: block;
  }
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-image {
  display: inline-block;
  min-width: 179px;
  max-width: 179px;
  position: relative;
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-image::after {
  content: "";
  background: rgba(0, 0, 0, 0.15);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
@media (max-width: 1199px) {
  .sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-image {
    min-width: 140px;
    max-width: 140px;
  }
}
@media (max-width: 991px) {
  .sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-image {
    min-width: 180px;
    max-width: 180px;
  }
}
@media (max-width: 576px) {
  .sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-image {
    min-width: 140px;
    max-width: 140px;
  }
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-content {
  max-width: 150px;
  width: 100%;
  line-height: 1;
  margin-top: -6px;
}
@media (max-width: 991px) {
  .sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-content {
    margin-top: 0;
  }
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-content span {
  font-family: var(--font-open-sans);
  font-size: 10px;
  font-weight: 600;
  line-height: 11px;
  color: var(--white-color);
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-content h6 {
  font-family: var(--font-open-sans);
  font-size: 10px;
  font-weight: 600;
  line-height: 11px;
  color: var(--white-color);
  margin-bottom: 0;
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-content h6 a {
  color: var(--white-color);
}
.sm-news-insignt-sec .news-insight-wrap .single-sm-news .news-content .date {
  font-family: var(--font-open-sans);
  font-size: 7px;
  font-weight: 400;
  line-height: 11px;
  color: var(--white-color);
}

.next-section-btn-area {
  padding: 27px 0 23px;
}
.next-section-btn-area .next-sec {
  height: 21px;
  width: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  transition: 0.45a;
}
.next-section-btn-area .next-sec:hover {
  background-color: var(--black-color);
}

.primary-btn {
  font-family: var(--font-open-sans);
  font-size: 10px;
  font-weight: 600;
  line-height: 7.74px;
  text-align: center;
  color: var(--white-color);
  background-color: var(--black-color);
  padding: 7px 16px;
  overflow: hidden;
  transition: all 0.5s ease 0s;
  position: relative;
  z-index: 1;
  display: inline-block;
}
.primary-btn::before {
  content: "";
  width: 0%;
  height: 100%;
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: -1;
  background: var(--primary-color);
  transition: all 0.5s ease 0s;
}
.primary-btn:hover::before {
  width: 100%;
}
.primary-btn.two:hover {
  color: var(--black-color);
}
.primary-btn.two:hover::before {
  background-color: var(--white-color);
}

.section-title {
  margin-bottom: 20px;
}
.section-title h6 {
  font-family: var(--font-open-sans);
  font-size: 9px;
  font-weight: 600;
  line-height: 11px;
  color: var(--black-color);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  text-transform: uppercase;
}
.section-title.white h6 {
  color: var(--white-color);
}

.about-section {
  background-color: #454545;
  padding: 50px 0 70px;
}
.about-section .section-title .line {
  margin-top: 14px;
}
.about-section .main-content p {
  color: var(--white-color);
}
.about-section.respinsible .main-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.about-section.respinsible .main-content ul li {
  position: relative;
  font-family: var(--font-open-sans);
  font-size: 18px;
  font-weight: 300;
  line-height: 25px;
  color: var(--white-color);
  margin-bottom: 27px;
  padding-left: 18px;
}
.about-section.respinsible .main-content ul li::after {
  content: "";
  width: 8px;
  height: 1px;
  background-color: var(--white-color);
  position: absolute;
  left: 0;
  top: 12px;
}

.client-section {
  background-color: #F1F2F2;
  padding: 50px 0 70px;
}
.client-section .section-title .line {
  margin-top: 14px;
}
.client-section.bg-white {
  background-color: var(--white-color);
}
.client-section.respinsible .main-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.client-section.respinsible .main-content ul li {
  position: relative;
  font-family: var(--font-open-sans);
  font-size: 18px;
  font-weight: 300;
  line-height: 25px;
  color: var(--black-color);
  margin-bottom: 27px;
  padding-left: 18px;
}
.client-section.respinsible .main-content ul li::after {
  content: "";
  width: 8px;
  height: 1px;
  background-color: var(--black-color);
  position: absolute;
  left: 0;
  top: 12px;
}
.client-section.respinsible .main-content ul li a {
  color: var(--black-color);
  font-weight: 700;
}
.client-section.conatct .footer-contact {
  line-height: 1;
}
.client-section.conatct .footer-contact h6 {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
}
.client-section.conatct .footer-contact p a {
  color: var(--black-color);
}
.client-section.conatct .footer-contact .contact-info {
  margin-bottom: 22px;
}
.client-section.conatct .footer-contact .contact-info a {
  font-weight: 300;
  font-size: 18px;
  color: var(--black-color);
  display: block;
  margin-bottom: 5px;
}

.expertise-card {
  background-color: #231F20;
  padding: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  position: relative;
  transition: 0.45s;
}
.expertise-card h6 {
  margin-bottom: 0;
  margin-top: -20px;
  text-align: center;
  transition: 0.45s;
}
.expertise-card h6 a {
  font-family: var(--font-open-sans);
  font-size: 14px;
  font-weight: 700;
  line-height: 14px;
  color: var(--white-color);
  transition: 0.45s;
}
.expertise-card h6 a span {
  font-weight: 400;
  display: block;
  transition: 0.45s;
}
.expertise-card .card-button {
  position: absolute;
  left: 50%;
  bottom: 25px;
  transform: translateX(-50%);
  transition: 0.45s;
}
.expertise-card .card-button a {
  height: 20px;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #414042;
  transition: 0.45s;
}
.expertise-card .card-button a img {
  transition: 0.45s;
}
.expertise-card .card-button a:hover {
  background-color: var(--black-color);
}
.expertise-card:hover {
  background-color: var(--primary-color);
}
.expertise-card:hover .card-button a img {
  transform: rotate(90deg);
}
.expertise-card.two {
  background-color: rgba(0, 0, 0, 0.4);
  transition: 0.45s;
}
.expertise-card.two .card-button a:hover {
  background-color: var(--black-color);
}
.expertise-card.two:hover {
  background-color: var(--primary-color);
}
.expertise-card.two:hover .card-button a img {
  transform: rotate(90deg);
}

.bg-red {
  background-color: var(--primary-color);
}
.bg-red .line {
  background-color: var(--white-color);
}

.team-section {
  padding: 55px 0 100px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
}

.people-card .people-img {
  display: block;
  margin-bottom: 5px;
}
@media (max-width: 767px) {
  .people-card .people-img {
    width: 100%;
  }
  .people-card .people-img img {
    width: 100%;
  }
}
.people-card .people-content {
  line-height: 1;
}
.people-card .people-content h6 {
  margin-bottom: 0;
}
.people-card .people-content h6 a {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 600;
  line-height: 13px;
  color: var(--white-color);
}
.people-card .people-content span {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 300;
  line-height: 13px;
  color: var(--white-color);
}
.people-card.two .people-content h6 a {
  color: var(--black-color);
}
.people-card.two .people-content span {
  color: var(--black-color);
}

.insight-card {
  background-color: var(--primary-color);
  padding: 18px 20px 20px;
}
.insight-card .category-and-content h5 {
  margin-bottom: 0;
  line-height: 0;
  border-bottom: 1px solid #fff;
  padding-bottom: 10px;
  margin-bottom: 5px;
}
.insight-card .category-and-content h5 a {
  font-family: var(--font-open-sans);
  font-size: 10px;
  font-weight: 600;
  line-height: 11px;
  color: var(--white-color);
}
.insight-card .category-and-content h6 {
  margin-bottom: 0;
  line-height: 1;
}
.insight-card .category-and-content h6 a {
  font-family: var(--font-open-sans);
  font-size: 10.22px;
  font-weight: 700;
  line-height: 15.9px;
  text-align: left;
  color: var(--white-color);
}
.insight-card .category-and-content p {
  font-family: var(--font-open-sans);
  font-size: 10.22px;
  font-weight: 300;
  line-height: 15.9px;
  color: var(--white-color);
  margin-bottom: 0;
}
.insight-card .arrow-btn {
  padding-top: 25px;
  margin-bottom: 13px;
}
.insight-card .arrow-btn a {
  height: 12px;
  width: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white-color);
}
.insight-card .arrow-btn a svg {
  stroke: var(--title-color);
}
.insight-card .meta {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 18px;
  border-top: 1px solid #fff;
  border-bottom: 1px solid #fff;
  padding-top: 2px;
  padding-bottom: 2px;
  line-height: 1;
}
.insight-card .meta li {
  line-height: 0;
  position: relative;
}
.insight-card .meta li::after {
  content: "";
  height: 8px;
  width: 1px;
  background-color: var(--white-color);
  position: absolute;
  right: -10px;
  top: 55%;
  transform: translateY(-50%);
}
.insight-card .meta li:last-child::after {
  display: none;
  visibility: hidden;
}
.insight-card .meta li a {
  font-family: var(--font-open-sans);
  font-size: 6.81px;
  font-weight: 700;
  line-height: 15.9px;
  color: var(--white-color);
}
.insight-card.bg-grey {
  background-color: #454546;
}
.insight-card.bg-grey .arrow-btn a {
  background-color: #3A3A3C;
}
.insight-card.bg-grey .arrow-btn a svg {
  stroke: var(--white-color);
}

.insight-section .insight-big-image {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.insight-section .insight-big-image img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.insight-slider-section .insight-big-image {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.insight-slider-section .insight-big-image img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (max-width: 991px) {
  .insight-slider-section .slider-btn-group {
    display: none;
  }
}
.insight-slider-section .slider-btn-group .slider-btn {
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(69, 69, 70, 0.3);
  border-radius: 50%;
  position: absolute;
  left: -100px;
  top: 50%;
  transform: translateY(-50%);
}
.insight-slider-section .slider-btn-group .slider-btn.next-btn {
  right: -100px;
  left: unset;
}
.insight-slider-section.two {
  margin-top: -400px;
}

footer {
  background-color: #231F20;
  padding: 30px 0 70px;
}
footer .footer-content-wrap {
  border-top: 1px solid var(--primary-color);
  border-bottom: 1px solid var(--primary-color);
  padding: 25px 0 60px;
}
footer .footer-content-wrap .footer-main-menu {
  margin: 0;
  padding: 0;
  list-style: none;
}
footer .footer-content-wrap .footer-main-menu li {
  line-height: 0;
}
footer .footer-content-wrap .footer-main-menu li a {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 600;
  line-height: 11px;
  color: var(--white-color);
  margin-bottom: 6px;
  display: block;
  transition: 0.45s;
}
footer .footer-content-wrap .footer-main-menu li a:hover {
  text-decoration: underline;
}
footer .footer-content-wrap .footer-contact {
  line-height: 1;
}
footer .footer-content-wrap .footer-contact h6 {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 700;
  line-height: 16px;
  color: var(--white-color);
  margin-bottom: 0;
}
footer .footer-content-wrap .footer-contact p {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 300;
  line-height: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0;
}
footer .footer-content-wrap .footer-contact .contact-info {
  padding-top: 22px;
}
footer .footer-content-wrap .footer-contact .contact-info a {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 300;
  line-height: 16px;
  color: rgba(255, 255, 255, 0.7);
  transition: 0.45s;
}
footer .footer-content-wrap .footer-contact .contact-info a:hover {
  color: var(--white-color);
}
footer .footer-content-wrap .footer-navbar__submenu {
  margin: 0;
  padding: 0;
  list-style: none;
}
footer .footer-content-wrap .footer-navbar__submenu li {
  line-height: 0;
}
footer .footer-content-wrap .footer-navbar__submenu li a {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 300;
  line-height: 16px;
  color: rgba(255, 255, 255, 0.7);
  transition: 0.45s;
}
footer .footer-content-wrap .footer-navbar__submenu li a:hover {
  color: var(--white-color);
}
footer .footer-content-wrap .footer-social p {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 300;
  line-height: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 12px;
}
footer .footer-content-wrap .footer-social ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
footer .footer-content-wrap .footer-social ul li svg{
  width: 25px;
  height: 25px;
  fill: var(--white-color);

}
footer .copyright-text {
  line-height: 1;
  padding-top: 12px;
}
footer .copyright-text p {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 300;
  line-height: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0;
}

.industry-card {
  border: 1px solid var(--black-color);
  min-height: 234px;
  padding: 15px 20px;
  height: 100%;
}
@media (max-width: 576px) {
  .industry-card {
    min-height: unset;
    padding-bottom: 50px;
  }
}
.industry-card h6 {
  font-family: var(--font-open-sans);
  font-size: 10px;
  font-weight: 700;
  line-height: 11px;
  color: var(--title-color);
  margin-bottom: 10px;
  position: relative;
  padding-bottom: 6px;
}
@media (max-width: 991px) {
  .industry-card h6 {
    font-size: 14px;
    line-height: 19px;
  }
}
.industry-card h6::after {
  content: "";
  background-color: var(--primary-color);
  opacity: 0.5;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 1px;
}
.industry-card p {
  font-family: var(--font-open-sans);
  font-size: 11px;
  font-weight: 300;
  line-height: 13.6px;
  color: var(--black-color);
  margin-bottom: 10px;
}
@media (max-width: 991px) {
  .industry-card p {
    font-size: 14px;
    line-height: 19px;
  }
}

.arbitration-content h5 {
  margin-bottom: 7px;
  font-family: var(--font-open-sans);
  font-size: 18px;
  font-weight: 600;
  line-height: 11px;
  color: var(--black-color);
}

.services-list-area h6 {
  font-family: var(--font-open-sans);
  font-size: 13px;
  font-weight: 700;
  line-height: 25px;
  text-align: left;
  color: var(--black-color);
  margin-bottom: 0;
}
.services-list-area ul {
  padding: 0;
  list-style: none;
}
.services-list-area ul li {
  font-family: var(--font-open-sans);
  font-size: 13px;
  font-weight: 300;
  line-height: 20px;
  color: var(--title-color);
  position: relative;
  padding-left: 10px;
  margin-bottom: 7px;
}
.services-list-area ul li::after {
  content: "";
  height: 2px;
  width: 2px;
  border-radius: 50%;
  background-color: var(--black-color);
  position: absolute;
  left: 0;
  top: 10px;
}

.people-section {
  margin-top: -190px;
}

.sector-experince {
  margin: 0;
  padding: 0;
  list-style: none;
  padding: 12px 0 25px;
}
.sector-experince li {
  font-family: var(--font-open-sans);
  font-size: 18px;
  font-weight: 300;
  line-height: 25px;
  color: var(--white-color);
}

.pagination {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-top: 35px;
}
.pagination li {
  font-family: var(--font-open-sans);
  font-size: 8px;
  font-weight: 700;
  line-height: 11px;
  color: var(--black-color);
}
.pagination li a {
  color: #939598;
}
.pagination li .active a {
  color: var(--black-color);
}

.subscribe-section {
  background-color: var(--primary-color);
  padding: 60px 0;
}
.subscribe-section input {
  width: 100%;
  padding: 10px 20px;
  background-color: var(--white-color);
  font-family: var(--font-open-sans);
  font-size: 8px;
  font-weight: 700;
  line-height: 11px;
  color: var(--black-color);
  height: 48px;
}
.subscribe-section input::-moz-placeholder {
  color: var(--black-color);
}
.subscribe-section input::placeholder {
  color: var(--black-color);
}

.single-insight-conetnt {
  background-color: #F1F2F2;
  padding: 50px 0 70px;
}
.single-insight-conetnt .section-title .line {
  margin-top: 14px;
}
.single-insight-conetnt h5 {
  font-family: var(--font-open-sans);
  font-size: 18px;
  font-weight: 700;
  line-height: 25px;
  color: var(--black-color);
}
.single-insight-conetnt .blockquote-container {
  max-width: 225px;
  min-width: 225px;
  background-color: var(--primary-color);
  padding: 13px 15px 50px;
  position: relative;
  overflow: hidden;
  float: right;
}
@media (max-width: 767px) {
  .single-insight-conetnt .blockquote-container {
    float: unset;
    margin-bottom: 25px;
    max-width: 100%;
    min-width: 100%;
  }
}
.single-insight-conetnt .blockquote-container p {
  font-family: var(--font-open-sans);
  font-size: 13.64px;
  font-weight: 300;
  line-height: 18px;
  color: var(--white-color);
  margin-bottom: 0;
}
.single-insight-conetnt .blockquote-container img {
  position: absolute;
  bottom: 15px;
  right: 15px;
}/*# sourceMappingURL=style.css.map */