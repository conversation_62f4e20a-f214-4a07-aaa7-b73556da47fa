# Galib Theme - Backend Usage Guide

## How to Create Different Post Types in Publii

### 🧑‍💼 Team Member Posts

**Step-by-step process:**

1. **Create New Post**
   - Go to Posts → Add new post
   - Write the team member's name as the title

2. **Select Template**
   - In the right sidebar, find "Template" dropdown
   - Select **"🧑‍💼 Team Member Profile"**

3. **Add Featured Image**
   - Upload a square photo (recommended 400x400px or larger)
   - This will be displayed as a circular profile photo

4. **Add Team Tag**
   - In the Tags section, add "Team" tag
   - This helps organize and filter team content

5. **Add Custom Fields**
   - Scroll down to "SEO & Advanced" section
   - Click on "Additional data"
   - Add the following JSON structure:

```json
{
  "position": "Job Title Here",
  "bio": "Write a detailed biography about the team member...",
  "social_links": "{\"linkedin\": \"https://linkedin.com/in/username\", \"twitter\": \"https://twitter.com/username\", \"github\": \"https://github.com/username\"}",
  "skills": "JavaScript, React, Node.js, Python, Leadership"
}
```

**Field Explanations:**
- `position`: Job title or role (e.g., "Senior Developer", "Marketing Manager")
- `bio`: Detailed biography in HTML format
- `social_links`: JSON string with social media URLs
- `skills`: Comma-separated list of skills and expertise

---

### 📝 Enhanced Blog Posts

**Step-by-step process:**

1. **Create New Post**
   - Go to Posts → Add new post
   - Write your blog post title

2. **Select Template**
   - In the right sidebar, find "Template" dropdown
   - Select **"📝 Enhanced Blog Post"**

3. **Add Blog Tag**
   - In the Tags section, add "Blog" tag

4. **Add Custom Fields**
   - In "SEO & Advanced" → "Additional data":

```json
{
  "category": "Technology",
  "reading_time": "8 min read",
  "featured": true
}
```

**Field Explanations:**
- `category`: Choose from "Technology", "Lifestyle", "Business", "Travel", "Other"
- `reading_time`: Estimated reading time (e.g., "5 min read")
- `featured`: Set to `true` for featured posts, `false` or omit for regular posts

---

## Template Selection in Backend

When creating a post, you'll see these template options in the dropdown:

- **Default** - Standard post template
- **🧑‍💼 Team Member Profile** - For team member pages
- **📝 Enhanced Blog Post** - For blog posts with extra features

## Tags for Organization

Use these tags to organize your content:

- **Team** - For all team member posts
- **Blog** - For all blog posts
- Add additional tags as needed for further categorization

## Custom Fields Reference

### Team Member Fields

| Field | Type | Required | Example |
|-------|------|----------|---------|
| position | Text | No | "Senior Developer" |
| bio | HTML | No | "&lt;p&gt;John has 10+ years...&lt;/p&gt;" |
| social_links | JSON | No | '{"linkedin": "url", "twitter": "url"}' |
| skills | Text | No | "JavaScript, React, Node.js" |

### Blog Post Fields

| Field | Type | Required | Options |
|-------|------|----------|---------|
| category | Select | No | Technology, Lifestyle, Business, Travel, Other |
| reading_time | Text | No | "5 min read" |
| featured | Boolean | No | true/false |

## Tips for Better Content Management

### For Team Posts:
1. **Use square images** for profile photos (they display as circles)
2. **Keep position titles concise** but descriptive
3. **Write engaging bios** that highlight achievements and personality
4. **Include relevant social links** (LinkedIn, Twitter, GitHub, etc.)
5. **List key skills** that are relevant to their role

### For Blog Posts:
1. **Choose appropriate categories** for better organization
2. **Estimate reading time** accurately (roughly 200 words per minute)
3. **Use featured sparingly** for your best content
4. **Write compelling titles** and meta descriptions
5. **Use proper heading structure** (H2, H3, etc.) in content

## Troubleshooting

### Template Not Showing
- Make sure you've saved the theme configuration
- Restart Publii if templates don't appear
- Check that the theme is set to "Galib" in site settings

### Custom Fields Not Working
- Ensure JSON format is valid (use a JSON validator)
- Check field names match exactly (case-sensitive)
- Make sure quotes are properly escaped in JSON

### Styling Issues
- Verify the post has the correct template selected
- Check that the appropriate tags are added
- Ensure featured images are uploaded

## Content Examples

### Example Team Member JSON:
```json
{
  "position": "Lead Frontend Developer",
  "bio": "<p>Sarah is a passionate frontend developer with over 8 years of experience creating beautiful, user-friendly web applications. She specializes in React and Vue.js and loves mentoring junior developers.</p><p>When she's not coding, Sarah enjoys hiking, photography, and contributing to open-source projects.</p>",
  "social_links": "{\"linkedin\": \"https://linkedin.com/in/sarahdev\", \"twitter\": \"https://twitter.com/sarahcodes\", \"github\": \"https://github.com/sarahdev\"}",
  "skills": "React, Vue.js, TypeScript, CSS3, UI/UX Design, Team Leadership"
}
```

### Example Blog Post JSON:
```json
{
  "category": "Technology",
  "reading_time": "12 min read",
  "featured": true
}
```

## Publishing Workflow

1. **Create** your post with the appropriate template
2. **Add** custom fields and tags
3. **Preview** to check formatting and styling
4. **Publish** when ready
5. **Regenerate** the site to see changes live

Remember: Always preview your posts before publishing to ensure the custom fields are displaying correctly and the styling looks good!
